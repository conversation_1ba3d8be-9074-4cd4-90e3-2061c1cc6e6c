<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground"
        app:cardBackgroundColor="@color/background_selected"
        app:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="13dp">

            <TextView
                android:id="@+id/textView1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:text="Item"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:layout_constraintEnd_toStartOf="@+id/textView2"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintHorizontal_weight="2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/txt_items"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{model.items}"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toStartOf="@+id/textView2"
                app:layout_constraintHorizontal_weight="2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView1"
                tools:text="Paha Atas, Data Lembut, Paha Bawah, Sapi Pangg ang, Ada lagi.., asdad,adad" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingLeft="16dp"
                android:text="@string/customer"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:layout_constraintEnd_toStartOf="@+id/textView3"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@+id/textView1"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/poppins"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:text="@{model.customer}"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toStartOf="@+id/textView3"
                app:layout_constraintStart_toStartOf="@+id/textView2"
                app:layout_constraintTop_toBottomOf="@+id/textView2"
                tools:text="Via Vallen" />

            <TextView
                android:id="@+id/textView3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingLeft="16dp"
                android:text="@string/table"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:layout_constraintEnd_toStartOf="@+id/textView4"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@+id/textView2"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/poppins"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:text="@{model.table}"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toStartOf="@+id/txt_grandtotal"
                app:layout_constraintStart_toStartOf="@+id/textView3"
                app:layout_constraintTop_toBottomOf="@+id/textView3"
                tools:text="09" />

            <TextView
                android:id="@+id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                android:gravity="right"
                android:text="Total"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/txt_grandtotal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_semibold"
                android:maxLines="1"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView4"
                app:textCurrency="@{model.grandTotal}"
                tools:text="Rp147.000" />

            <View
                android:id="@+id/view1"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginTop="7dp"
                android:background="@color/text_grey_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txt_items" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="7dp"
                android:textColor="@color/grey_light"
                app:format="@{`dd MMMM yyyy HH:ss`}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view1"
                app:timeMillisToDate="@{model.timeCreated}"
                tools:text="25 Augustus 2018 14:50" />

            <Button
                android:id="@+id/btn_merge"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:background="@android:color/transparent"
                android:text="Merge"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view1" />

            <Button
                android:id="@+id/btn_print"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:background="@android:color/transparent"
                android:text="Print"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toStartOf="@+id/btn_merge"
                app:layout_constraintTop_toBottomOf="@+id/view1" />

            <Button
                android:id="@+id/btn_edit"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:background="@android:color/transparent"
                android:text="Edit"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toStartOf="@+id/btn_print"
                app:layout_constraintTop_toBottomOf="@+id/view1" />

            <Button
                android:id="@+id/btn_pay"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:background="@android:color/transparent"
                android:text="@string/pay"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toStartOf="@+id/btn_edit"
                app:layout_constraintTop_toBottomOf="@+id/view1" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>
