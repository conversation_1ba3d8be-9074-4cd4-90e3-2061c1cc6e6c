<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="16dp"
        tools:context=".view.billing.BillingInformationActivity">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Batas Akhir Pembayaran"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_date_expired"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:text="Jumat, 23 April 2021 13:30 WIB"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:fontFamily="@font/poppins"
            android:text="METODE PEMBAYARAN"
            android:textColor="@color/text_grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_date_expired" />

        <TextView
            android:id="@+id/txt_bank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="BCA Virtual Account"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2" />

        <TextView
            android:id="@+id/textView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Nomor Virtual Account"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_bank" />

        <TextView
            android:id="@+id/txt_virtual_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="*****************"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView3" />

        <TextView
            android:id="@+id/txt_copy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="salin"
            android:paddingTop="9dp"
            android:paddingBottom="9dp"
            android:paddingLeft="26dp"
            android:textColor="@color/greeen_background"
            app:layout_constraintBottom_toBottomOf="@id/txt_virtual_account"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_virtual_account" />

        <TextView
            android:id="@+id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:fontFamily="@font/poppins"
            android:text="detail subscription"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_virtual_account" />

        <TextView
            android:id="@+id/txt_service_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Langganan Bulanan Perpanjang"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView4" />

        <TextView
            android:id="@+id/txt_period"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1 tahun"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_service_name" />

        <TextView
            android:id="@+id/txt_slot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3 slot"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_service_name" />

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="16dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_slot" />

        <TextView
            android:id="@+id/txt_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="Rp149,000"
            android:textColor="@color/text_orange"
            android:textSize="19sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view1" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
