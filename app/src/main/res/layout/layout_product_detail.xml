<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            tools:background="@color/background">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <View
                    android:id="@+id/view1"
                    android:layout_width="50dp"
                    android:layout_height="1dp"
                    android:background="@color/text_grey"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/view2"
                    android:layout_width="50dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/text_grey"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view1" />

                <ImageView
                    android:id="@+id/img_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:srcCompat="@drawable/met_ic_close"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/text_grey_light" />

                <ImageView
                    android:id="@+id/imageView1"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_marginTop="16dp"
                    android:scaleType="centerCrop"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:imgUrlText="@{model.photo}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:name="@{model.name}"
                    tools:src="@drawable/ayam" />

                <TextView
                    android:id="@+id/txt_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="9dp"
                    android:fontFamily="@font/poppins_medium"
                    android:text="@{model.name}"
                    android:textColor="#ffffff"
                    android:textSize="16sp"
                    app:layout_constraintStart_toEndOf="@id/imageView1"
                    app:layout_constraintTop_toTopOf="@id/imageView1"
                    tools:text="Nasi Goreng Spesial" />

                <TextView
                    android:id="@+id/txt_price"
                    symbol="@{`Rp`}"
                    textCurrency="@{model.priceSell}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/grey_light"
                    app:layout_constraintStart_toStartOf="@id/txt_name"
                    app:layout_constraintTop_toBottomOf="@id/txt_name"
                    tools:text="Rp16,500" />

                <TextView
                    android:id="@+id/txt_change_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/change_price"
                    android:textColor="@color/blue_background"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/txt_price" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fillViewport="true">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/textView7"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="9dp"
                        android:fontFamily="@font/poppins_medium"
                        android:text="Variant"
                        android:textColor="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recview_variant"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView7"
                        tools:listitem="@layout/list_item_names" />

                    <TextView
                        android:id="@+id/textView1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/poppins_medium"
                        android:text="Stock"
                        android:textColor="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/recview_variant" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/sw_availability"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@color/grey_light"
                        android:checked="@{model.stock.equals(`available`)}"
                        android:paddingLeft="16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/textView1"
                        tools:checked="true" />

                    <TextView
                        android:id="@+id/textView2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{String.valueOf(model.stockQty) + ` | `}"
                        android:textColor="@color/grey_light"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView1"
                        tools:text="14 | " />

                    <TextView
                        android:id="@+id/stock_availability"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.stock}"
                        android:textColor="@{model.stock.equals(`available`) ? @color/grey_light : @color/red_background}"
                        app:layout_constraintStart_toEndOf="@id/textView2"
                        app:layout_constraintTop_toTopOf="@id/textView2"
                        tools:text="available" />

                    <TextView
                        android:id="@+id/textView11"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/poppins_medium"
                        android:text="Discount and Voucher"
                        android:textColor="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView2" />

                    <TextView
                        android:id="@+id/txt_warn_disc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:text="@string/disc_voucher_enable"
                        android:textColor="@color/text_grey_light"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/textView11" />

                    <Button
                        android:id="@+id/btn_disc"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:layout_marginTop="9dp"
                        android:background="@drawable/round_grey"
                        android:paddingLeft="7dp"
                        android:paddingRight="7dp"
                        android:text="Discount"
                        android:textAllCaps="false"
                        android:textColor="@color/text_grey_light"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/txt_warn_disc" />

                    <Button
                        android:id="@+id/btn_voucher"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:layout_marginLeft="9dp"
                        android:background="@drawable/round_grey"
                        android:paddingLeft="7dp"
                        android:paddingRight="7dp"
                        android:text="Voucher"
                        android:textAllCaps="false"
                        android:textColor="@color/text_grey_light"
                        android:textSize="12sp"
                        app:layout_constraintStart_toEndOf="@id/btn_disc"
                        app:layout_constraintTop_toTopOf="@id/btn_disc" />

                    <TextView
                        android:id="@+id/textView8"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/poppins_medium"
                        android:text="Tax and Services"
                        android:textColor="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/btn_disc" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recview_taxes"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView8"
                        tools:listitem="@layout/list_item_names" />

                    <TextView
                        android:id="@+id/txt_warn_tax"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tax_service_disable"
                        android:textColor="@color/red_background"
                        android:textSize="13sp"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView8" />

                    <ImageView
                        android:id="@+id/img_more_1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="9dp"
                        android:rotation="90"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/recview_taxes"
                        app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
                        app:tint="@color/text_grey_light" />

                    <ImageView
                        android:id="@+id/img_more_2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:rotation="90"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/recview_taxes"
                        app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
                        app:tint="@color/text_grey_light" />

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="11dp"
                        android:fontFamily="@font/poppins_medium"
                        android:text="SKU"
                        android:textColor="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/img_more_1" />

                    <TextView
                        android:id="@+id/textView4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.sku ?? `-`}"
                        android:textColor="@color/grey_light"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView3"
                        tools:text="AY00001" />

                    <TextView
                        android:id="@+id/textView5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="11dp"
                        android:fontFamily="@font/poppins_medium"
                        android:text="Barcode"
                        android:textColor="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView4" />

                    <TextView
                        android:id="@+id/textView6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.barcode ?? `-`}"
                        android:textColor="@color/grey_light"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView5"
                        tools:text="BGAJ01KLO" />

                    <TextView
                        android:id="@+id/textView9"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="11dp"
                        android:fontFamily="@font/poppins_medium"
                        android:text="Category"
                        android:textColor="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView6" />

                    <TextView
                        android:id="@+id/textView10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.productSubCategoryName}"
                        android:textColor="@color/grey_light"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView9"
                        tools:text="Asisn Food" />

                    <Button
                        android:id="@+id/btn_add_cart"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="32dp"
                        android:background="@drawable/btn_round_green"
                        android:text="Add To Bill"
                        android:textColor="@color/white"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textView10" />

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/group_advance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:constraint_referenced_ids="textView5,textView4,textView6,textView3,textView9,textView10"
                        tools:visibility="visible" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>

        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
