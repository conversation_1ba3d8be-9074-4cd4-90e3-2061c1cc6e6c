<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="fragment"
            type="com.uniq.uniqpos.view.closeshift.CloseShiftFragment" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:padding="16dp">

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="9dp"
                android:paddingBottom="9dp"
                android:text="@string/close_cashier"
                android:textAppearance="@style/TextAppearance.AppCompat.Large"
                android:textColor="@color/white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="37dp"
                android:paddingLeft="1dp"
                android:text="@string/shift_info"
                android:textColor="@color/grey_light"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="26dp"
                android:paddingBottom="9dp"
                android:text="Masukan Sesuai Dengan Transaksi Yang Berlangsung"
                android:textColor="@color/text_grey_light"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textView1" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textView3">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_cash"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/cash"
                    android:inputType="number"
                    android:onTextChanged="@{fragment::onTextChanged}"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayout1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/card"
                    android:inputType="number"
                    android:onTextChanged="@{fragment::onTextChanged}"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <RelativeLayout
                android:id="@+id/layout_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/textInputLayout1">

            </RelativeLayout>

            <TextView
                android:id="@+id/title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:layout_marginTop="20dp"
                android:text="@{@string/cash + ` + ` + @string/card}"
                android:textColor="@color/grey_light"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textInputLayout1"
                tools:text="Cash + Card" />

            <TextView
                android:id="@+id/txt_cashpluscard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title_2"
                android:layout_marginLeft="3dp"
                android:text="0"
                android:textColor="@color/text_grey_light"
                android:textSize="19sp"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title_2"
                tools:text="1.950.000" />

            <CheckBox
                android:id="@+id/cb_print_daily"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:checked="false"
                android:text="print daily recap"
                android:textColor="@color/grey_light"
                android:theme="@style/CustomCheckBox"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_cashpluscard" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="centang untuk print rekap harian (semua shift) secara otomatis"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                android:textStyle="italic"
                app:layout_constraintStart_toStartOf="@id/cb_print_daily"
                app:layout_constraintTop_toBottomOf="@id/cb_print_daily" />

            <com.uniq.materialanimation.ButtonLoading
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="43dp"
                app:buttonColor="@color/blue_background"
                app:buttonText="@string/save"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cb_print_daily"
                app:textColor="#ffffff" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>
