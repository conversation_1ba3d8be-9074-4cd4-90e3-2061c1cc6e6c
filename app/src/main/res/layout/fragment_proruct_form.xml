<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:background="@color/background"
        tools:context=".view.productcatalogue.ProductFormFragment">

<!--        <ScrollView-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="0dp"-->
<!--            android:orientation="vertical"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent">-->

            <LinearLayout
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <RelativeLayout
                    android:id="@+id/vi_add_image"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/img_product"
                        android:layout_width="90dp"
                        android:layout_height="90dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/default_image" />

                    <TextView
                        android:id="@+id/textView1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_toEndOf="@+id/img_product"
                        android:text="@string/change_image"
                        android:textAllCaps="true"
                        android:textColor="@color/grey_light"
                        app:fontFamily="@font/poppins_semibold" />

                    <View
                        android:id="@+id/view1"
                        android:layout_width="150dp"
                        android:layout_height="1dp"
                        android:layout_below="@+id/textView1"
                        android:layout_marginLeft="16dp"
                        android:layout_toEndOf="@+id/img_product"
                        android:background="@color/background_selected" />

                    <TextView
                        android:id="@+id/txt_camera"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/view1"
                        android:layout_marginStart="16dp"
                        android:layout_toEndOf="@+id/img_product"
                        android:paddingTop="3dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="11dp"
                        android:text="@string/camera"
                        android:textAllCaps="true"
                        android:textColor="@color/text_grey_light" />

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/view1"
                        android:layout_marginTop="3dp"
                        android:layout_toEndOf="@+id/txt_camera"
                        android:text="|"
                        android:textColor="@color/text_grey_light" />

                    <TextView
                        android:id="@+id/txt_gallery"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/view1"
                        android:layout_marginTop="3dp"
                        android:layout_toEndOf="@+id/textView3"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="11dp"
                        android:text="@string/gallery"
                        android:textAllCaps="true"
                        android:textColor="@color/text_grey_light" />
                </RelativeLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/card_advanced_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="7dp"
                    app:cardBackgroundColor="@color/background_selected"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_advance"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/txt_advance_mode"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/poppins_semibold"
                            android:text="ADVANCED MODE"
                            android:textColor="@color/grey_light"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/switch_advanced_mode" />

                        <TextView
                            android:id="@+id/txt_advance_detail"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:text="@string/advance_mode_detail_show"
                            android:textColor="@color/text_grey_light"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/txt_advance_mode"
                            app:layout_constraintEnd_toStartOf="@+id/switch_advanced_mode" />

                        <TextView
                            android:id="@+id/txt_pinned_fields_info"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:text="Long-press on fields to pin them"
                            android:textColor="@color/text_grey_light"
                            android:textStyle="italic"
                            android:textSize="12sp"
                            android:layout_marginTop="4dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/txt_advance_detail"
                            app:layout_constraintEnd_toStartOf="@+id/switch_advanced_mode" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_advanced_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_product_name"
                        android:text="@{model.name}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/product_name"
                        android:inputType="textCapWords"
                        android:maxLength="30"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_category"
                    android:text="@{model.productcategoryName}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="@string/product_category"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_stock_management"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:checked="@{model.stockManagement == 1}"
                    android:text="@string/stock_management"
                    android:textColor="@color/grey_light" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_selling_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/selling_price"
                        android:text="@{model.priceSell.toString()}"
                        android:inputType="number"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_buy_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_buying_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/buying_price"
                        android:text="@{model.priceBuy.toString()}"
                        android:inputType="number"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_prod_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="@string/product_type"
                    android:text="@{model.productTypeName}"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_prod_subcategory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Product SubCategory"
                    android:text="@{model.productSubCategoryName}"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_purc_cateegory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Purchase Report Category"
                    android:text="@{model.purchaseReportCategoryName}"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_unit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Unit"
                    android:text="@{model.unitName}"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_barcode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_barcode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Barcode"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_sku"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_sku"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="SKU"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_active"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Menu Active"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />
            </LinearLayout>
<!--        </ScrollView>-->

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
