<?xml version="1.0" encoding="utf-8"?>
<layout>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="16sp"
        tools:context=".view.login.code.LoginWithCodeActivity">

        <TextView
            android:id="@+id/textView1"
            android:text="Verification Code"
            android:textColor="@color/grey_light"
            android:fontFamily="@font/poppins_semibold"
            android:textSize="21sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/textView2"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            app:layout_constraintStart_toStartOf="parent"
            android:text="Enter 6 digit code sent to your email"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            app:layout_constraintTop_toTopOf="@id/textView3"
            app:layout_constraintBottom_toBottomOf="@id/txt_email"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/background_selected"
            android:layout_width="0dp"
            android:layout_height="50dp"/>

        <TextView
            android:id="@+id/textView3"
            android:text="Verification code sent to"
            android:layout_marginLeft="9dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2"
            android:layout_marginTop="21dp"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_email"
            app:layout_constraintTop_toBottomOf="@id/textView3"
            app:layout_constraintStart_toStartOf="parent"
            android:text="<EMAIL>"
            android:layout_marginLeft="9dp"
            android:textSize="13sp"
            android:fontFamily="@font/poppins_semibold"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>



        <LinearLayout
            app:layout_constraintTop_toTopOf="@id/textView2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

        </LinearLayout>

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edt_code"
            android:inputType="number"
            app:layout_constraintTop_toBottomOf="@id/txt_email"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="25dp"
            android:layout_width="0dp"
            android:textColor="@color/white"
            android:theme="@style/InputStyle"
            android:layout_height="wrap_content"/>

        <com.uniq.materialanimation.ButtonLoading
            android:id="@+id/btn_login"
            app:buttonText="LOGIN"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
