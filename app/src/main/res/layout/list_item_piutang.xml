<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.PiutangEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_piutang"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground"
        android:paddingLeft="8dp"
        android:paddingTop="5dp"
        android:paddingRight="8dp"
        android:paddingBottom="5dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            android:text="@{model.sales.displayNota}"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="MDA2020082803" />

        <TextView
            android:id="@+id/txt_customer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_medium"
            android:maxLines="1"
            android:text="@{model.sales.customer}"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView1"
            tools:text="annasblackhat" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            android:textColor="@color/text_orange"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:textCurrency="@{model.unpaid}"
            tools:text="25.0000" />

        <TextView
            android:id="@+id/txt_paid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/poppins"
            android:maxLines="1"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:format="@{`dd MMM yyyy`}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView1"
            app:timeMillisToDate="@{model.dueDate}"
            tools:text="26 Sep 2018" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="5dp"
            android:text="due date: "
            android:textColor="@color/text_grey"
            app:layout_constraintEnd_toStartOf="@id/txt_paid"
            app:layout_constraintTop_toTopOf="@id/txt_paid" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
