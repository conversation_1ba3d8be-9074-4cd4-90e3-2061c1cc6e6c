<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?android:attr/selectableItemBackground"
        android:padding="9dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            android:text="@{model.name}"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@id/textView2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="XXL" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@id/textView3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/textView1"
            app:textCurrency="@{model.priceSell}"
            tools:text="Rp32,000" />

        <TextView
            android:id="@+id/textView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{String.valueOf(model.stockQty) + ` stock left`}"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            android:visibility="@{model.showStock ? View.VISIBLE : View.GONE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            tools:text="32 stock left" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
