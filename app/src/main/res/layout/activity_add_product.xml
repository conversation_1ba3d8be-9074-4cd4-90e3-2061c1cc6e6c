<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.productcatalogue.AddProductActivity">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_warning"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:padding="9dp"
            android:background="@color/text_orange"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <TextView
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:fontFamily="@font/poppins_medium"
                android:textColor="@color/white"
                app:layout_constraintEnd_toStartOf="@id/btn_try_ocr"
                app:layout_constraintBottom_toBottomOf="@id/btn_try_ocr"
                android:text="@string/try_ocr"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>

            <Button
                android:id="@+id/btn_try_ocr"
                android:text="@string/try_ai"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:background="@drawable/btn_round_orange"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="29dp"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@+id/btn_save"
            app:layout_constraintTop_toBottomOf="@id/layout_warning">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/img_product"
                        android:layout_width="90dp"
                        android:layout_height="90dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/default_image" />

                    <TextView
                        android:id="@+id/textView1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_toEndOf="@+id/img_product"
                        android:text="@string/change_image"
                        android:textAllCaps="true"
                        android:textColor="@color/grey_light"
                        app:fontFamily="@font/poppins_semibold" />

                    <View
                        android:id="@+id/view1"
                        android:layout_width="150dp"
                        android:layout_height="1dp"
                        android:layout_below="@+id/textView1"
                        android:layout_marginLeft="16dp"
                        android:layout_toEndOf="@+id/img_product"
                        android:background="@color/background_selected" />

                    <TextView
                        android:id="@+id/txt_camera"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/view1"
                        android:layout_marginStart="16dp"
                        android:layout_toEndOf="@+id/img_product"
                        android:paddingTop="3dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="11dp"
                        android:text="@string/camera"
                        android:textAllCaps="true"
                        android:textColor="@color/text_grey_light" />

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/view1"
                        android:layout_marginTop="3dp"
                        android:layout_toEndOf="@+id/txt_camera"
                        android:text="|"
                        android:textColor="@color/text_grey_light" />

                    <TextView
                        android:id="@+id/txt_gallery"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/view1"
                        android:layout_marginTop="3dp"
                        android:layout_toEndOf="@+id/textView3"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="11dp"
                        android:text="@string/gallery"
                        android:textAllCaps="true"
                        android:textColor="@color/text_grey_light" />
                </RelativeLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_product_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/product_name"
                        android:inputType="textCapWords"
                        android:maxLength="30"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="@string/product_category"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_stock_management"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:checked="true"
                    android:text="@string/stock_management"
                    android:textColor="@color/grey_light" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_selling_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/selling_price"
                        android:inputType="number"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_advance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp">

                    <TextView
                        android:id="@+id/txt_advance_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        app:layout_constraintTop_toTopOf="parent"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="ADVANCE MODE"
                        android:textColor="@color/grey_light"
                        app:layout_constraintStart_toStartOf="parent" />

                    <View
                        android:id="@+id/view2"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="@color/text_grey_light"
                        app:layout_constraintBottom_toBottomOf="@id/txt_advance_mode"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/txt_advance_detail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/advance_mode_detail_show"
                        android:textColor="@color/text_grey_light"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/view2" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_buy_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_buying_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/buying_price"
                        android:inputType="number"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_prod_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="@string/product_type"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_prod_subcategory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Product SubCategory"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_purc_cateegory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Purchase Report Category"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_unit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Unit"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_barcode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_barcode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Barcode"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_sku"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_sku"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="SKU"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_active"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Menu Active"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />
            </LinearLayout>
        </ScrollView>

        <Button
            android:id="@+id/btn_save"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/btn_round"
            android:text="@string/save"
            android:textColor="#ffff"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
