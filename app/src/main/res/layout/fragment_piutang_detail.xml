<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.PiutangEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="9dp"
        tools:context="com.uniq.uniqpos.view.piutang.PiutangDetailFragment">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            android:text="@string/invoice_no"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_invoice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:text="@{model.sales.displayNota}"
            android:textColor="@color/text_orange"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView1"
            tools:text="NS20180812197" />

        <TextView
            android:id="@+id/txt_show_sales"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/back_round_small"
            android:backgroundTint="@color/text_grey"
            android:paddingLeft="11dp"
            android:paddingRight="11dp"
            android:text="show transaction"
            android:textColor="#ffffff"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_invoice" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            android:text="@string/due_date"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:textColor="@color/text_orange"
            app:format="@{`dd MMM yyyy`}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView2"
            app:timeMillisToDate="@{model.dueDate}"
            tools:text="12 March 2018" />

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="7dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_show_sales" />

        <TextView
            android:id="@+id/textView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/poppins_semibold"
            android:text="@string/history"
            android:textColor="@color/greeen_background"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view1" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_history_payment"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/btn_send_invoice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView3"
            tools:listitem="@layout/list_item_history_paid_piutang" />

        <Button
            android:id="@+id/btn_send_invoice"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginRight="2dp"
            android:background="@color/blue_background"
            android:text="Send Invoice"
            android:textColor="#ffff"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btn_add_payment"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btn_add_payment"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginLeft="2dp"
            android:background="@color/blue_background"
            android:text="Add Payment"
            android:textColor="#FFFFFF"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btn_send_invoice"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
