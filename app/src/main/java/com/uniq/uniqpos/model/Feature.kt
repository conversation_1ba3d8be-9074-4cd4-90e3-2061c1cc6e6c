package com.uniq.uniqpos.model

import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.util.objectConverter

data class Feature(
    @field:SerializedName("splitbill")
    val splitbill: Boolean = false,

    @field:SerializedName("tableinput")
    val tableinput: Boolean = false,

    @field:SerializedName("void")
    val voidFeature: Boolean = false,

    @field:SerializedName("discount")
    val discount: <PERSON>olean = false,

    @field:SerializedName("splittable")
    val splittable: Boolean = false,

    @field:SerializedName("viewtotalachievement")
    val viewtotalachievement: Boolean = false,

    @field:SerializedName("authorization")
    val authorizationResponse: Any? = null,

    @field:SerializedName("movetable")
    val movetable: Boolean = false,

    @field:SerializedName("numberofcustomers")
    val numberofcustomers: Boolean = false,

    @field:SerializedName("member")
    val member: <PERSON><PERSON><PERSON> = false,

    @field:SerializedName("notelist")
    val notelist: <PERSON><PERSON><PERSON> = false,

    @field:Serialized<PERSON>ame("reprint_closeregister")
    val reprintCloseregister: <PERSON><PERSON><PERSON> = false,

    @field:SerializedName("viewtransactionhistory")
    val viewtransactionhistory: Boolean = false,

    @field:SerializedName("waitingtime")
    val waitingtime: Boolean = false,

    @field:SerializedName("free")
    val free: Boolean = false,

    @field:SerializedName("viewcloseregister")
    val viewcloseregister: Boolean = false,

    @field:SerializedName("employeename")
    val employeename: Boolean = false,

    @field:SerializedName("refund")
    val refund: Boolean = false,

    @field:SerializedName("offlinemode")
    val offlineMode: Boolean = false,

    @field:SerializedName("transaction_zerostock")
    val transactionZeroStock: Boolean = false,

    @field:SerializedName("transaction_requires_tag")
    val transactionRequireTag: Boolean = false
) {
    var authFeature: RoleMobile = RoleMobile()
        get() = authorizationResponse?.objectConverter(RoleMobile::class.java) ?: RoleMobile()
}