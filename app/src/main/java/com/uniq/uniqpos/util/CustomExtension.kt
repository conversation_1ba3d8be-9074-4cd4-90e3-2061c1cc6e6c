package com.uniq.uniqpos.util

import android.app.Activity
import android.app.ProgressDialog
import android.content.*
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.DynamicDrawableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.animation.TranslateAnimation
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.browser.customtabs.CustomTabColorSchemeParams
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bugsnag.android.Bugsnag
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.google.android.gms.security.ProviderInstaller
import com.google.android.material.textfield.TextInputEditText
import com.google.gson.Gson
import com.google.gson.internal.LinkedTreeMap
import com.google.gson.reflect.TypeToken
import com.google.gson.stream.MalformedJsonException
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.PromotionEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.model.*
import com.uniq.uniqpos.databinding.LayoutToastBinding
import com.uniq.uniqpos.model.ExperimentConfig
import com.uniq.uniqpos.model.Feature
import com.uniq.uniqpos.model.PromotionSales
import com.uniq.uniqpos.model.RoleMobile
import com.uniq.uniqpos.util.security.AES
import com.wdullaer.materialdatetimepicker.date.DatePickerDialog
import com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
import kotlinx.coroutines.TimeoutCancellationException
import okhttp3.MediaType
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.HttpException
import retrofit2.Response
import timber.log.Timber
import java.io.File
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.URL
import java.net.UnknownHostException
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern
import kotlin.collections.ArrayList
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * Created by ANNASBlackHat on 13/10/2017.
 */

fun <T : Any> Call<ServerResponseList<T>>.awaitList(func: (List<T>) -> Unit): Boolean {
    val response = try {
        execute()
    } catch (e: Exception) {
        Bugsnag.leaveBreadcrumb("request err ${request().url()} $e")
        Timber.d("Request to server with url ${request().url()} error (ServerResponseList/awaitList) $e ")
        null
    }

    response?.let {
        if (response.isSuccessful) {
            val items = response.body()
            items?.data?.let { func(it) }
            return true
        }
        if(response.code() == 404){
            Timber.i(">>> error request not found at ${request().url()}")
            Bugsnag.notify(Throwable("request not found: `${request().url()}`"))
        }
    }
    return false
}

fun <T : Any> Call<ServerResponseList<T>>.awaitListBase(func: (ServerResponseList<T>) -> Unit): Boolean {
    val data = try {
        execute()
    } catch (e: Exception) {
//        Timber.i("Request to server with url ${request().url()} error (ServerResponseList/awaitListBase) $e ")
        Bugsnag.leaveBreadcrumb("request err ${request().url()} $e")
        Timber.i("[[ERROR]] request failed ${request().url()} - $e")
        if (e is com.google.gson.JsonSyntaxException){
            Bugsnag.notify(e)
        }
        null
    }
//    data?.let {
//        if(data.isSuccessful && data.code() == 200){
//            data.body()?.let { func(it) }
//            return true
//        }
//    }
    data?.takeIf { it.isSuccessful && it.code() == 200 }?.let {
        it.body()?.let { func(it) }
        return true
    }
    return false
}

fun <T : Any> Call<ServerResponse<T>>.await(func: (T?) -> Unit): Boolean {
    val data = try {
        execute()
    } catch (e: Exception) {
//        Timber.i("Request to server with url ${request().url()} error (ServerResponse/await) $e ")
        Bugsnag.leaveBreadcrumb("request err ${request().url()} $e")
        Timber.d("request failed ${request().url()}")
        null
    }

    data?.takeIf { it.isSuccessful && it.code() == 200 }?.let {
        it.body()?.let { items ->
            if (items.status) {
                func(items.data)
                return true
            }
        }
    }
    return false
}

//running async
fun <T : Any> Call<ServerResponse<T>>.awaitBase(func: (ServerResponse<T>?) -> Unit): Boolean {
    val data = try {
        execute()
    } catch (e: Exception) {
        Bugsnag.leaveBreadcrumb("request err ${request().url()} $e")
//        Timber.i("Request to server with url ${request().url()} error (ServerResponse/awaitBase) $e ")
        Timber.d("request failed ${request().url()}")
        null
    }
//    data?.let {
//        if(data.isSuccessful){
//            func(data.body())
//            return true
//        }
//    }
    data?.takeIf { it.isSuccessful }?.let {
        func(data.body())
        return true
    }
    return false
}

fun <T> Call<T>.awaitAsync(
    success: (response: Response<T>) -> Unit,
    failure: (t: Throwable) -> Unit,
    context: Activity? = null,
    refreshLayout: SwipeRefreshLayout? = null,
    isShowProgressDialog: Boolean = true,
    progressbar: ProgressBar? = null,
    dialogMessage: String = "Loading..."
) {

    var dialog: ProgressDialog? = null
    context?.takeIf { isShowProgressDialog }?.let {
        if (refreshLayout == null) dialog = ProgressDialog.show(context, null, dialogMessage)
    }
    refreshLayout?.isRefreshing = true
    progressbar?.visibility = View.VISIBLE

    enqueue(object : Callback<T> {
        override fun onResponse(call: Call<T>?, response: Response<T>) {
            try {
                context?.takeIf { !it.isFinishing }?.let {
                    dialog?.takeIf { it.isShowing }?.apply { dismiss() }
                    refreshLayout?.isRefreshing = false
                    progressbar?.visibility = View.GONE
                }

                success(response)
                if (response.code() == 402 || response.code() == 401) {
                    val tmpContext = context ?: refreshLayout?.context
                }
            } catch (e: Exception) {
                Timber.i("onResponse at requesting ${request().url()} Error - $e")
                failure(Throwable(e.message))
            }
        }

        override fun onFailure(call: Call<T>?, t: Throwable) {
            dialog?.takeIf { it.isShowing }?.apply { dismiss() }
            refreshLayout?.isRefreshing = false
            progressbar?.visibility = View.GONE

            println("xxx response error $t")
            context?.let {
                it.showMessage(t.readableError(it), "Error")
            }
            failure(t)
        }
    })
}

suspend fun <T> Call<T>.await(): T {
    return suspendCoroutine { continuation ->
        enqueue(object : Callback<T> {
            override fun onFailure(call: Call<T>, t: Throwable) {
                Timber.i("[REQUEST FAILURE] : $t ")
                continuation.resumeWithException(t)
            }

            override fun onResponse(call: Call<T>, response: Response<T>) {
                response.body()?.takeIf { response.isSuccessful }?.let { body ->
                    continuation.resume(body)
                } ?: kotlin.run {
                    Timber.i("[REQUEST ERROR] code : ${response.code()} | msg : '${response.message()}' | err body : ${response.errorBody()} | resp : $response")
                    continuation.resumeWithException(NetworkException(response))
                }
            }
        })
    }
}

suspend fun <T> await(fn: () -> T): T {
    return suspendCoroutine { continuation ->
        BACKGROUND.submit {
            continuation.resume(fn())
        }
    }
}

suspend fun <T : Any> awaitBackgroundList(fn: () -> List<T>): List<T> {
    return suspendCoroutine {  continuation ->
        BACKGROUND.submit {
            continuation.resume(fn())
        }
    }
}

class NetworkException(private val response: Response<*>? = null) : Exception(response?.message()) {
    fun getResponse() = response
}

class WarnException(val msg: String) : Exception(msg) {}

fun Int?.toCurrency(prefix: String = ""): String {
    val num = NumberFormat.getNumberInstance()
    return try {
        prefix + num.format(this)
    } catch (e: Exception) {
        "${prefix}0"
    }
}

fun String.fromCurrency(): Int {
    val num = NumberFormat.getNumberInstance()
    return try {
        num.parse(this).toInt()
    } catch (e: Exception) {
        0
    }
}

fun TextInputEditText.fromCurrency(): Int {
    return text?.toString().safe().fromCurrency()
}

fun String?.toRequestBody(): RequestBody {
//    return RequestBody.create("text/plain".toMediaTypeOrNull(), this ?: "")
    return RequestBody.create(MediaType.parse("text/plain"), this ?: "")
}

fun View.drawableBackground(drawable: Int) {
    background = ContextCompat.getDrawable(context, drawable)
}

fun View.setVisible(isShow: Boolean, invisible: Int = View.GONE) {
    if (isShow && (visibility == View.GONE || visibility == View.INVISIBLE)) {
        visibility = View.VISIBLE
    } else if (!isShow && (visibility == View.VISIBLE)) {
        visibility = invisible
    }
}

fun View.slideUp() {
    val animate = TranslateAnimation(
        0f, // fromXDelta
        0f, // toXDelta
        height.toFloat(), // fromYDelta
        0f
    )                // toYDelta
    animate.duration = 100
    animate.fillAfter = true
    startAnimation(animate)
}

fun View?.hideKeyboard(ctx: Context?) {
    this?.takeIf { ctx != null }?.let { view ->
        val imm = ctx!!.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }
}

fun StringBuilder.appendnl(value: String? = " ", lineAfter: Int = 1, lineBefore: Int = 0) {
    for (i in 0 until lineBefore) this.appendln()
    this.append("$value")
    for (i in 0 until lineAfter) this.appendln()
}

fun String.change(add: String?): String {
    val tmpVal = this.substring((add?.length ?: 0))
    return add + tmpVal
}

fun String.loop(w: Int): String {
    var newVal = ""
    for (i in 0 until w) newVal += this
    return newVal
}

fun String.safeRepeat(count: Int?): String {
    return if (count != null && count > 0) repeat(count) else ""
}

//align left
fun String.width(width: Int, max: Int = 32, before: Int = 0): String {
    try {
        val wordSplit = StringBuilder()
        var cursor = 0
        do {
            var suffix = ""
            val start = cursor
            val end = if ((start + width) < length) start + width else length
            val cutWord = this.substring(start, end)
            var lastSpace =
                if (cutWord.contains(" ") && end != length) cutWord.lastIndexOf(" ") else cutWord.length
            if (lastSpace < (width / 2) && (width - 1) < cutWord.length) {
                lastSpace = width - 1
                cursor--
                suffix = "-"
            }
            cursor += lastSpace + 1
            suffix += when {
                cursor < length -> {
                    " ".safeRepeat(max - (before + lastSpace)) + "\n"
                }
                else -> {
                    " ".safeRepeat(width - (before + lastSpace))
                }
            }
            wordSplit.append(cutWord.substring(0, lastSpace) + suffix)
        } while (cursor < this.length)
        return wordSplit.toString()
    } catch (e: Exception) {
        //OLD LOGIC - just in case above logic is error
        Bugsnag.notify(e)
        Timber.i("[EXCEPTION] extension width error for text : '$this' | max $max | width : $width | before $before")
        var newValue = this
        for (i in length until width) {
            newValue += " "
        }
        val newLength = newValue.length
        if (newLength > width) {
            var space = ""
            for (i in (before + width) until max) space += " "
            newValue = newValue.substring(0, width) + "$space\n" + newValue.substring(width, length)
            for (i in (newLength - width) until width + before) newValue += " "
        }
        return newValue
    }
}

//align right
fun String.widthRight(w: Int): String {
    val newValue = StringBuilder()
    for (i in 0 until (w - this.length)) {
        newValue.append(" ")
    }
    return newValue.append(this).toString()
}

fun String.center(width: Int, flanks: String = " "): String {
    val newValue = StringBuilder()
    when {
        length == width -> {
            newValue.append(this)
        }
        length < width -> {
            val center = width / 2
            val flank = center - (this.length / 2)
            for (i in 0 until flank) newValue.append(flanks)
            newValue.append(this)
            for (i in (newValue.toString().length) until width) newValue.append(flanks)
        }
        else -> {
            val wordSplit = StringBuilder()
            do {
                val start = wordSplit.length
                val end = if ((start + width) < length) start + width else length
                val cutWord = this.substring(start, end)
                val lastSpace =
                    if (cutWord.contains(" ")) cutWord.lastIndexOf(" ") else cutWord.length
                wordSplit.appendln(cutWord.substring(0, lastSpace))
            } while (wordSplit.length < this.length)

            wordSplit.split("\n").forEach {
                if (it.length > width) {
                    newValue.appendln(it)
                    Timber.i("[ERROR] length should not greater than width. text : '$this' | width $width")
                    Bugsnag.notify(Exception("center extension: length should not greater than width. text : $this"))
                } else {
                    newValue.appendln(it.center(width))
                }
            }
        }
    }
    return newValue.toString()
}

fun String.toDate(format: String = "dd-MM-yyyy"): Date? {
    if(isBlank())return null
    val sdf = SimpleDateFormat(format)
    try {
        return sdf.parse(this)
    } catch (e: Exception) {
        Timber.i("String.toDate Error - $e")
    }
    return null
}

fun String.toTimeMillis(format: String = "dd-MM-yyyy"): Long {
    val sdf = SimpleDateFormat(format)
    try {
        return sdf.parse(this).time
    } catch (e: Exception) {
        Timber.i("String.toDate Error - $e")
    }
    return 0
}

fun String.indexAt(string: String, occurrence: Int = 0): Int {
    var lastIndexFound = -1
    for (i in 0 until (occurrence + 1)) {
        lastIndexFound = indexOf(string, lastIndexFound + 1)
    }
    return lastIndexFound
}

fun String.splitWithSplitters(vararg splitters: String): List<String> {
    val regexPattern = splitters.joinToString("|") { Regex.escape(it) }
    return this.split(Regex(regexPattern))
}

fun String?.displayNotaFormat(): String {
    return this?.replace(Constant.PRINTER_CODE_CUT, "")?.replace(Constant.PRINTER_CODE_LOGO, "")
        ?: kotlin.run { "" }
}

fun String.addPrefixOnNewLines(prefix: String): String {
    return replace(Regex("^|\\n", RegexOption.MULTILINE), "$0$prefix")
}

fun Date.dateFormat(format: String? = null): String {
    val sdf = SimpleDateFormat(format ?: "dd-MM-yyyy")
    return sdf.format(this)
}

fun Date.dateTimeFormat(format: String? = null): String {
    val sdf = SimpleDateFormat(format ?: "dd-MM-yyyy HH:mm")
    return sdf.format(this)
}

fun Long.dateTimeFormat(format: String? = null): String {
    val sdf = SimpleDateFormat(format ?: "dd-MM-yyyy HH:mm")
    return sdf.format(Date(this))
}

fun Long.dateFormat(format: String? = null): String {
    val sdf = SimpleDateFormat(format ?: "dd-MM-yyyy")
    return sdf.format(Date(this))
}

fun LongRange.diffSecond(): Int {
    val diff = first - last
    return (diff / (1000)).toInt()
}

fun LongRange.diffMinutes(): Int {
    val diff = first - last
    return (diff / (60 * 1000)).toInt()
}

fun Long.diffDay(daySecond: Long = System.currentTimeMillis()): Int {
    val diffTime = daySecond - this
    val diffResult = diffTime / (1000 * 60 * 60 * 24)
    return diffResult.toInt()
}

fun Long.diffHour(daySecond: Long = System.currentTimeMillis()): Int {
    val diffTime = daySecond - this
    val diffResult = diffTime / (1000 * 60 * 60)
    return diffResult.toInt()
}

fun Long.diffMinute(timeNow: Long = System.currentTimeMillis()): Int {
    val diff = timeNow - this
    val diffMinutes = diff / (60 * 1000)
    return diffMinutes.toInt()
}

fun Int.forcePositive(): Int {
    return if (this < 0) {
        this * -1
    } else
        this
}

fun Int.force(isNegative: Boolean): Int {
    return if (isNegative && this > 0) {
        this * -1
    } else if (!isNegative && this < 0) {
        this * -1
    } else {
        this
    }
}

fun Int.min(minimum: Int = 0): Int {
    return if (this < minimum) minimum else this
}

//isApply : when this max role should apply
fun Int.max(maximum: Int = 0, isApply: Boolean = true): Int {
    return if (isApply && this > maximum) maximum else this
}

fun Int.NonZero(default: Int = 1): Int {
    return if (this <= 0) default else this
}

//change integer number to negative if match the condition
fun Int.minusIf(condition: Boolean = true): Int {
    return if (condition && this > 0) this * -1 else this
}

fun Context.showMessage(
    msg: String?, title: String? = null, positiveAction: DialogInterface.OnClickListener? = null,
    negativeAction: DialogInterface.OnClickListener? = null, positiveMsg: String = "OK",
    negativeMsg: String? = "Cancel", onDismissListener: DialogInterface.OnDismissListener? = null
) {
    Timber.i("[[DIALOG]] $msg")
    val alert = AlertDialog.Builder(this)
        .setTitle(title)
        .setMessage(msg)
        .setOnDismissListener(onDismissListener)
        .setPositiveButton(positiveMsg, positiveAction)
        .setNegativeButton(negativeMsg, negativeAction)
        .setOnDismissListener(onDismissListener)
//    positiveAction?.let { alert.setNegativeButton(negativeMsg, negativeAction) }
    msg?.let { alert.show() }
}

enum class ToastViewType {
    DEFAULT, CUSTOM
}

enum class Level {
    DEFAULT, INFO, WARNING, ERROR
}

fun Context.toast(
    @StringRes resource: Int,
    duration: Int = Toast.LENGTH_SHORT,
    level: Level = Level.DEFAULT
) {
    toast(getString(resource), duration, level)
}

fun Context.toast(
    msg: String? = null,
    duration: Int = Toast.LENGTH_SHORT,
    level: Level = Level.DEFAULT
) {
    if (msg == null) return
    fun show() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val toast = Toast(applicationContext)
            val viBinding = LayoutToastBinding.inflate(LayoutInflater.from(this), null, false)
            viBinding.txtMessage.text = msg

            when (level) {
                Level.ERROR -> viBinding.root.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(this, R.color.red_background))
                Level.WARNING -> viBinding.root.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(this, R.color.orange_background))
                Level.INFO -> viBinding.root.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(this, R.color.greeen_background))
                else -> Timber.i("")

            }
            toast.view = viBinding.root
            toast.show()
        } else {
            Toast.makeText(applicationContext, msg, duration).show()
        }
    }
    if (Looper.myLooper() == Looper.getMainLooper()) {
        Timber.d("toast first")
        show()
    } else {
        Timber.d("toast second")
        Handler(Looper.getMainLooper()).post {
            show()
        }
    }
    Timber.i("[[TOAST]] $msg")
}

private fun initToastView(context: Context, msg: String, level: Level): Toast {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        val toast = Toast(context)
        val viBinding = LayoutToastBinding.inflate(LayoutInflater.from(context), null, false)
        viBinding.txtMessage.text = msg

        when (level) {
            Level.ERROR -> viBinding.root.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.red_background))
            Level.WARNING -> viBinding.root.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.orange_background))
            Level.INFO -> viBinding.root.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.greeen_background))
            else -> Timber.i("undefinded")
        }
        toast.view = viBinding.root
        return toast
    } else {
        return Toast.makeText(context.applicationContext, "", Toast.LENGTH_SHORT)
    }
}

fun Context.launchUrl(url: String?) {
    if (url.isNullOrBlank()) return
    val builder = CustomTabsIntent.Builder()
//    builder.setToolbarColor(ContextCompat.getColor(this, R.color.colorPrimary))
//    add share button to overflow men
//    builder.addDefaultShareMenuItem()
    builder.setShareState(CustomTabsIntent.SHARE_STATE_ON)
    // add menu item to oveflow
//     builder.addMenuItem("MENU_ITEM_NAME", pendingIntent)
    // show website title
    builder.setShowTitle(true)
    builder.setUrlBarHidingEnabled(false)
    // modify back button icon
//     builder.setCloseButtonIcon()
    // menu item icon
//     builder.setActionButton(bitmap, "Android", pendingIntent, true)
    // animation for enter and exit of tab            builder.setStartAnimations(this, android.R.anim.fade_in, android.R.anim.fade_out)
    builder.setExitAnimations(this, android.R.anim.fade_in, android.R.anim.fade_out)

    val params = CustomTabColorSchemeParams.Builder()
        .setToolbarColor(ContextCompat.getColor(this, R.color.colorPrimary))
        .build()
    builder.setColorSchemeParams(CustomTabsIntent.COLOR_SCHEME_DARK, params)

    val customTabsIntent = builder.build()

    try {
        val customTabHelper = CustomTabHelper()
        val packageName = customTabHelper.getPackageNameToUse(this, url)
        if (packageName != null) {
            customTabsIntent.intent.setPackage(packageName)
        }
        customTabsIntent.launchUrl(this, Uri.parse(url))
    } catch (e: Exception) {
        Bugsnag.notify(e)
        Timber.i("launching custom tab err: $e")
        val intentLaunch = Intent(Intent.ACTION_VIEW)
        intentLaunch.data = Uri.parse(url)
        try {
            startActivity(intentLaunch)
        } catch (e: Exception) {
            toast("browser might not installed", level = Level.ERROR)
        }
    }
}

//TODO: complete launch url
//ref: https://developers.google.com/web/updates/2020/07/custom-tabs-android-11
fun Context.launchUrlV2(url: String?) {
    if (url.isNullOrBlank()) {
        Timber.i("can not launch empty url..")
        return
    }
}

fun Context.launchUrlApi30(uri: Uri): Boolean {
    val nativeAppIntent = android.content.Intent(android.content.Intent.ACTION_VIEW, uri)
        .addCategory(Intent.CATEGORY_BROWSABLE)
        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REQUIRE_NON_BROWSER)
    return try {
        startActivity(nativeAppIntent)
        true
    } catch (e: ActivityNotFoundException) {
        false
    }
}

fun Context.launchUrlBeforeApi30(uri: Uri): Boolean {
    val pm = packageManager
    val intentBrowser = Intent()
        .setAction(Intent.ACTION_VIEW)
        .addCategory(Intent.CATEGORY_BROWSABLE)
        .setData(Uri.fromParts("http", "", null))

//    val genericResolverList = extra

    return true
}

fun Context.installTls12() {
    if (Build.VERSION.SDK_INT < 21) {
        try {
            ProviderInstaller.installIfNeededAsync(
                this,
                object : ProviderInstaller.ProviderInstallListener {
                    override fun onProviderInstallFailed(errorCode: Int, recoveryIntent: Intent?) {
                        toast("updating security failed... $errorCode")
                    }

                    override fun onProviderInstalled() {
                        toast("updating security succeed")
                    }
                })
        } catch (e: GooglePlayServicesRepairableException) {
            // Prompt the user to install/update/enable Google Play services.
            GoogleApiAvailability.getInstance()
                .showErrorNotification(this, e.connectionStatusCode)
        } catch (e: GooglePlayServicesNotAvailableException) {
            // Indicates a non-recoverable error: let the user know.
            showMessage("Google Play Service is not installed on your device. Our service can not be accessed without it")
        }
    }
}

fun Context.receiptLogoFile(): File? {
    return getJson(
        SharedPref.OUTLET_DATA,
        Outlet::class.java
    )?.receiptLogo?.takeIf { it.isNotEmpty() }?.let { receiptLogo ->
        val fileName =
            if (receiptLogo.startsWith("https://") || receiptLogo.startsWith("http://")) {
                val path = URL(receiptLogo).path
                path.substring(path.lastIndexOf("/") + 1)
            } else receiptLogo

        File(filesDir, "logo/$fileName")
    } ?: kotlin.run { null }
}

fun Context?.readString(resource: Int, default: String = ""): String {
    return this?.let { getString(resource) } ?: default
}

fun Context?.isAppInstalled(appId: String): Boolean {
    if (this == null) {
        return false
    }

    return try {
        packageManager.getPackageInfo(appId, 0)
        true
    } catch (e: Exception) {
        false
    }
}

fun Throwable.readableError(context: Context? = null, isOverrideMsg: Boolean = true): String =
    when (this) {
        is ConnectException -> context?.getString(R.string.connect_exception)
            ?: "We cannot connect to the server! \nPlease make sure you have stable internet connection"
        is UnknownHostException -> context?.getString(R.string.no_internet_access)
            ?: "No Internet Access"
        is SocketTimeoutException -> context?.getString(R.string.connection_timeout)
            ?: "Connection Timeout! \nPlease make sure you have stable internet connection!"
        is MalformedJsonException -> context?.getString(R.string.json_mailformed_exception)
            ?: "Failed to read response from server!"
        is NetworkException -> message?.takeIf { it.isNotEmpty() } ?: kotlin.run {
            if (getResponse()?.code() == 500) context?.getString(R.string.internal_server_error)
                ?: "Sorry our system is having a bit trouble right now, \nplease try again later!"
            else "Please check your internet connection!"
        }
        is TimeoutCancellationException -> "Please make sure you have stable internet connection!"
        is WarnException -> msg
        is HttpException -> {
            if (this.code() == 502) {
                "502 Bad Gateway\nConnection to server failed"
            } else {
                this.message()
            }
        }
        else -> {
            if (isOverrideMsg) {
                //kotlinx.coroutines.JobCancellationException
                if (message == "Job was cancelled") {
                    "process aborted..."
                } else {
                    Timber.i(">> Unknown Exception - $this ($message)")
                    Bugsnag.notify(this)
                    context?.getString(R.string.encounter_error)
                        ?: "Sorry, we encounter some error. Please try again later"
                }

            } else {
                this.message ?: "Unknown error"
            }
        }
    }

fun TextInputEditText.toInt(): Int {
    return try {
        text.toString().toInt()
    } catch (e: Exception) {
        0
    }
}

fun TextInputEditText.value(): String {
    return try {
        text.toString()
    } catch (e: Exception) {
        ""
    }
}

fun AutoCompleteTextView.value(): String {
    return text.toString()
}

fun TextInputEditText.valueToInt(): Int {
    return try {
        text.toString().toInt()
    } catch (e: Exception) {
        0
    }
}

fun MaterialBetterSpinner.value(): String {
    return text.toString()
}

fun String?.safeToInt(default: Int = 0): Int {
    return try {
        this?.toInt() ?: kotlin.run { 0 }
    } catch (e: Exception) {
        Timber.d("safeToInt throw exception: $e")
        default
    }
}

fun String?.safeToLong(default: Long = 0): Long {
    return try {
        this?.toLong() ?: kotlin.run { 0L }
    } catch (e: Exception) {
        default
    }
}

fun String?.isNumber(): Boolean {
    return try {
        this?.toInt()
        true
    } catch (e: Exception) {
        false
    }
}

fun String?.copyToClipboard(context: Context?, description: String = "uniq") {
    this?.let {
        val clipboard = context?.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
        val clip = ClipData.newPlainText(description, it)
        clipboard?.setPrimaryClip(clip)
    }
}

fun String.takeNumber(): Int {
    var num = ""
    forEach {
        if (it.toString().isNumber()) {
            num += it
        }
    }

    return num.safeToInt()
}

fun String?.safe(default: String = ""): String = this ?: default

fun String?.toLower(): String {
    return this?.let { toLowerCase(Locale.getDefault()) } ?: ""
}

fun String?.removeHtmlTag(): String {
    if (this == null) {
        return ""
    }

    return this.replace(Regex("<(/|)(p|br|b)>"), "")
}

fun String?.equalIgnoreCase(comparator: String?) =
    this.safe().equals(comparator.safe(), ignoreCase = true)

fun String?.isPhoneNumber(): Boolean {
    if (this == null) return false
    return (startsWith("62") || startsWith("08")) && isNumeric() && length in 9..15
}


//1 : true, 0 or any number else : false
fun Int?.toBoolean(default: Boolean = false): Boolean {
    return this?.let { num ->
        num == 1
    } ?: kotlin.run { default }
}

fun Int?.safe(default: Int = 0): Int {
    return this ?: default
}

fun Long?.safe(default: Long = 0): Long {
    return this ?: default
}

fun Double?.safe(default: Double = 0.0): Double {
    return this ?: default
}

fun Boolean.toInt(): Int {
    return if (this) 1 else 0
}

fun Boolean?.safe(default: Boolean = false): Boolean {
    return this ?: default
}

fun Activity?.handleIntent(type: String, vararg data: String) {
    when (type) {
        "phone" -> {
            val intent = Intent(Intent.ACTION_DIAL)
            intent.data = Uri.parse("tel:${data[0]}")
            this?.startActivity(intent)
        }
    }
}


fun Context?.isGooglePlayServicesAvailable(activity: Activity? = null): Boolean {
    return this?.let { context ->
        val availability = GoogleApiAvailability.getInstance()
        val status = availability.isGooglePlayServicesAvailable(context)
        if (status != ConnectionResult.SUCCESS) {
            activity?.apply {
                if (availability.isUserResolvableError(status)) {
                    availability.getErrorDialog(this, status, 2404)?.show()
                }
            }
            return false
        }
        true
    } ?: run { false }
}

fun EditText?.safeToInt(): Int {
    return try {
        this?.text?.toString()?.toInt() ?: kotlin.run { 0 }
    } catch (e: Exception) {
        0
    }
}

fun EditText.isEmptyValue(): Boolean {
    return text.toString().isEmpty()
}

fun EditText.simpleOnTextChanged(onChange: (String) -> Unit): TextWatcher {
    val watcher = object : TextWatcher {
        override fun afterTextChanged(p0: Editable?) {}
        override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
        override fun onTextChanged(cs: CharSequence?, p1: Int, p2: Int, p3: Int) {
            onChange(cs.toString())
        }
    }
    this.addTextChangedListener(watcher)
    return watcher
}

fun TextInputEditText.liveToCurrencyAndWatch(onChange: (String) -> Unit): TextWatcher {
    val watcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence, i: Int, i1: Int, i2: Int) {}
        override fun onTextChanged(s: CharSequence, i: Int, i1: Int, i2: Int) {
            if (text.toString().trim { it <= ' ' }.isNotEmpty()) {
                removeTextChangedListener(this)
                try {
                    var strNumber = s.toString().replace(",", "")
                    strNumber = strNumber.replace(".", "")
                    val number = Integer.parseInt(strNumber)
                    val currency = NumberFormat.getInstance().format(number.toLong())
                    setText(currency)
                    setSelection(currency.length)
                } catch (e: Exception) {
                }
                addTextChangedListener(this)
            }
            onChange(text.toString())
        }

        override fun afterTextChanged(editable: Editable) {}
    }
    addTextChangedListener(watcher)
    return watcher
}

fun TextInputEditText.setAsDateinput(
    fragmentManager: FragmentManager,
    maxDate: Calendar? = null,
    minDate: Calendar? = null,
    selectedDate: Calendar = Calendar.getInstance(),
    dateFormat: String = "dd-MM-yyyy"
) {
    isFocusable = false
    setOnClickListener {
        val dpd = DatePickerDialog.newInstance(
            { _, year, monthOfYear, dayOfMonth ->
                val tgl = String.format("%02d", dayOfMonth) + "-" + String.format(
                    "%02d",
                    monthOfYear + 1
                ) + "-" + year
                setText(tgl)
            },
            selectedDate.get(Calendar.YEAR),
            selectedDate.get(Calendar.MONTH),
            selectedDate.get(Calendar.DAY_OF_MONTH)
        )
        dpd.version = DatePickerDialog.Version.VERSION_1
        minDate?.let { dpd.minDate = it }
        maxDate?.let { dpd.maxDate = it }
        dpd.setTitle("Pilih Tanggal")
        dpd.show(fragmentManager, "date-input")
    }
}

fun <T : Any> Map<String, List<T>>.getKey(index: Int): String? {
    var loop = 0
    forEach {
        if (loop++ == index) {
            return it.key
        }
    }
    return null
}

fun ByteArray.toHex(): String {
    val result = StringBuffer()
    val HEX_CHARS = "0123456789ABCDEF".toCharArray()

    forEach {
        val octet = it.toInt()
        val firstIndex = (octet and 0xF0).ushr(4)
        val secondIndex = octet and 0x0F
        result.append(HEX_CHARS[firstIndex])
        result.append(HEX_CHARS[secondIndex])
    }

    return result.toString()
}

fun String.isNumeric(): Boolean {
    return matches("-?\\d+(\\.\\d+)?".toRegex())
}

fun String.isValidIpAddress(): Boolean {
    val ipPattern =
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\$"
    val pattern = Pattern.compile(ipPattern)
    if (!pattern.matcher(this).matches()) {
        return false
    }
    return true
}

fun String?.isValidEmail(): Boolean {
    if (this == null) {
        return false
    }
    val emailPattern =
        "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$"
    val pattern = Pattern.compile(emailPattern)
    val matcher = pattern.matcher(this)
    val isValid = matcher.matches()
    return isValid
}

fun String.dateToLong(dateFormat: String = "dd-MM-yyyy"): Long {
    val date = SimpleDateFormat(dateFormat).parse(this)
    return date.time
}

fun String.upperCaseWord(): String {
    val strBuilder = StringBuilder(toLowerCase())
    var found = 0
    do {
        found = strBuilder.indexOf(" ", found) + 1
        if (found < strBuilder.length)
            strBuilder.setCharAt(found, strBuilder[found].toUpperCase())
    } while (found > 0)

    return strBuilder.toString()
}

fun Boolean.visibility(hideState: Int = View.GONE): Int {
    return if (this) View.VISIBLE else hideState
}

fun <T> Any?.objectConverter(cls: Class<T>): T? {
    return this?.let {
        return try {
            val data = it as LinkedTreeMap<String, Any>
            val json = Gson().toJson(data)
            Gson().fromJson(json, cls)
        } catch (e: Exception) {
            Timber.i("objectConverter Error to : ${cls.simpleName}")
            null
        }
    } ?: kotlin.run { null }
}

fun Context.role(): RoleMobile {
    return sharedPref().role()
}

fun SharedPref.role(): RoleMobile {
    var employeeRole: String? = null
    try {
        val employee = getJson(SharedPref.EMPLOYEE_DATA, EmployeeEntity::class.java)
        employeeRole = employee?.roleMobile.safe("{}")
        return Gson().fromJson(employeeRole.replace("[]", "{}"), RoleMobile::class.java)
    } catch (e: Exception) {
        Timber.i("role() Error : $e, role: '$employeeRole' | ${getString(SharedPref.EMPLOYEE_DATA)}")
        Bugsnag.notify(e)
    }
    return RoleMobile()
}

fun Fragment.role(): RoleMobile {
    return context?.role() ?: RoleMobile()
}

fun ArrayList<ByteArray>.merge(): ByteArray {
    val size = sumBy { it.size }
    val result = ByteArray(size)
    var offset = 0
    forEach { byte ->
        System.arraycopy(byte, 0, result, offset, byte.size)
        offset += byte.size
    }
    return result
}

fun <T> List<T>.getSafe(index: Int): T? {
    return if (index in 0 until size) this[index] else null
}

fun String?.encrypt(): String {
    this?.let {
        try {
            return if (isNotEmpty()) AES().encrypt(this)?.toHex() ?: "" else ""
        } catch (e: Exception) {
            Timber.i("[encrypt] err when encrypt this : $this")
        }
    }
    return ""
}

fun String?.decrypt(): String {
    this?.let {
        return if (isNotEmpty()) AES.decryptStr(this) else ""
    }
    return ""
}

fun Context?.employee(): Employee? {
    return this?.let { getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java) }
}

fun Context?.outlet(): Outlet? {
    return this?.let { getJson(SharedPref.OUTLET_DATA, Outlet::class.java) }
}

fun Outlet?.experimentConfig(): ExperimentConfig {
    return this?.experimentConfig?.let {
        Gson().fromJson(it, ExperimentConfig::class.java)
    } ?: ExperimentConfig()
}

fun Context?.shiftOpen(): ShiftOpen? {
    return this?.let { getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java) }
}

fun Context.outletFeature(): Feature {
    return getJson(SharedPref.OUTLET_DATA, Outlet::class.java)?.feature?.let {
        Gson().fromJson(it, Feature::class.java)
    } ?: Feature()
}

fun Context.employeeList(): List<EmployeeEntity> {
    return sharedPref().employeeList()
}

fun SharedPref.employeeList(): List<EmployeeEntity> {
    val employeeList = ArrayList<EmployeeEntity>()
    getString(SharedPref.EMPLOYEE_LIST)?.let { data ->
        val type = object : TypeToken<List<EmployeeEntity>>() {}.type
        val json = Gson().fromJson<List<EmployeeEntity>>(data.trim(), type)
        json?.let { employeeList.addAll(json) }
    }
    return employeeList
}

fun Employee.toEntity(): EmployeeEntity {
    return EmployeeEntity(
        employeeId, address, role, level, lastLogin, accessStatusMobile, photo, 0, accessMode,
        adminFkid, jabatanFkid, null, roleMobile, phone, 0, accessStatusWeb, name,
        dataStatus, dateJoin, email, pin
    )
}

fun EmployeeEntity.simplify(): Employee {
    return Employee(
        this.address,
        this.role,
        this.roleMobile,
        this.dateJoin?.dateFormat(),
        this.accessStatusMobile,
        this.dataCreated.toString(),
        this.accessMode,
        this.jabatanFkid,
        "",
        this.userActivationExpired,
        this.pin,
        this.phone,
        this.employeeId,
        this.accessStatusWeb,
        this.name,
        this.dataStatus,
        this.email,
        this.level
            ?: 0,
        this.lastLogin,
        this.photo,
        this.adminFkid,
        this.dateJoin
    )
}

fun MemberEntity.covertToMember(): Member {
    return Member(
        this.memberId,
        this.phone,
        name = this.name,
        typeName = this.typeName.safe(),
        typeId = this.typeId
    )
}

fun Context.deactivatePrinterList(): ArrayList<String> {
    var deactivePrinterList = ArrayList<String>()
    var deactivePrinter: String? = getLocalDataString(SharedPref.PRINTER_DEACTIVE, null)
    deactivePrinter?.takeIf { it.isNotEmpty() }?.let {
        val type = object : TypeToken<ArrayList<String>>() {}.type
        deactivePrinterList = Gson().fromJson(deactivePrinter, type)
    }
    return deactivePrinterList
}

fun SharedPref.deactivatePrinterList(): ArrayList<String> {
    var deactivePrinterList = ArrayList<String>()
    var deactivePrinter: String? = getString(SharedPref.PRINTER_DEACTIVE, null)
    deactivePrinter?.takeIf { it.isNotEmpty() }?.let {
        val type = object : TypeToken<ArrayList<String>>() {}.type
        deactivePrinterList = Gson().fromJson(deactivePrinter, type)
    }
    return deactivePrinterList
}

fun PromotionEntity.simplify(): PromotionSales {
    return PromotionSales(
        promotionId, name ?: "[No Name]", promotionTypeId
            ?: 0, minOrder = minOrder, maxQtyPromo = maxQtyPromo
    )
}

fun Promotion.simplify(): PromotionSales {
    return PromotionSales(
        promotionCode = this.code,
        pomotionTypeFkid = this.typeId,
        minOrder = this.minOrder,
        name = this.name
            ?: "promo",
        promotionId = this.promotionId,
        maxQtyPromo = this.maxQtyPromo
    )
}

fun Promotion.toEntity(): PromotionEntity {
    val promotionProducts =
        kotlin.collections.ArrayList<com.uniq.uniqpos.data.local.entity.PromotionProduct>()
    this.promotionDetail?.products?.forEach { product ->
        promotionProducts.add(
            com.uniq.uniqpos.data.local.entity.PromotionProduct(
                productDetailId = product.productDetailFkid,
                type = product.type,
                qty = product.qty
            )
        )
    }
    return PromotionEntity(
        promotionProduct = promotionProducts,
        memberType = null,
        name = this.name
    )
}

interface ProgressListener {
    fun stopProgress()
}

fun Button.setProgressLoading(onClick: (ProgressListener) -> Unit) {
    val originalText = text.toString()
    // create progress drawable
    val progressDrawable = CircularProgressDrawable(context).apply {
        // let's use large style just to better see one issue
        setStyle(CircularProgressDrawable.LARGE)
        setColorSchemeColors(Color.WHITE)

        //bounds definition is required to show drawable correctly
        val size = (centerRadius + strokeWidth).toInt() * 2
        setBounds(0, 0, size, size)
    }

    // create a drawable span using our progress drawable
    val drawableSpan = object : DynamicDrawableSpan() {
        override fun getDrawable() = progressDrawable
    }

    // create a SpannableString like "Loading [our_progress_bar]"
    val spannableString = SpannableString(" ").apply {
        setSpan(drawableSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    val callback = object : Drawable.Callback {
        override fun unscheduleDrawable(who: Drawable, what: Runnable) {}
        override fun scheduleDrawable(who: Drawable, what: Runnable, `when`: Long) {}
        override fun invalidateDrawable(who: Drawable) {
            invalidate()
        }
    }
    progressDrawable.callback = callback

    val listener = object : ProgressListener {
        override fun stopProgress() {
            progressDrawable.stop()
            isEnabled = true
            text = originalText
        }
    }

    setOnClickListener {
        isEnabled = false
        text = spannableString
        progressDrawable.start()
        onClick(listener)
    }
}

fun Button.startProgress(progress: (ProgressListener) -> Unit) {
    val originalText = text.toString()
    isEnabled = false

    // create progress drawable
    val progressDrawable = CircularProgressDrawable(context).apply {
        // let's use large style just to better see one issue
        setStyle(CircularProgressDrawable.LARGE)
        setColorSchemeColors(Color.WHITE)

        //bounds definition is required to show drawable correctly
        val size = (centerRadius + strokeWidth).toInt() * 2
        setBounds(0, 0, size, size)
    }

    // create a drawable span using our progress drawable
    val drawableSpan = object : DynamicDrawableSpan() {
        override fun getDrawable() = progressDrawable
    }

    // create a SpannableString like "Loading [our_progress_bar]"
    val spannableString = SpannableString(" ").apply {
        setSpan(drawableSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    val callback = object : Drawable.Callback {
        override fun unscheduleDrawable(who: Drawable, what: Runnable) {}
        override fun scheduleDrawable(who: Drawable, what: Runnable, `when`: Long) {}
        override fun invalidateDrawable(who: Drawable) {
            invalidate()
        }
    }
    progressDrawable.callback = callback

    val listener = object : ProgressListener {
        override fun stopProgress() {
            progressDrawable.stop()
            isEnabled = true
            text = originalText
        }
    }

    text = spannableString
    progressDrawable.start()
    progress(listener)
}

fun String.takeMax(max: Int, suffix: String = "..."): String {
    return if (length > max) {
        substring(0, max) + suffix
    } else {
        this
    }
}

fun Int.readablePromoType(ctx: Context? = null, default: String = "[promo]"): String {
    return when (this) {
        Constant.PROMO_TYPE_SPECIAL_PRICE -> "Promo Special Price" //4
        Constant.PROMO_TYPE_DISCOUNT -> "Promo Discount" //5
        Constant.PROMO_TYPE_FREE -> "Promo Free Item" //6
        Constant.PROMO_TYPE_SPECIAL_PRICE_MEMBER -> "Promo Special Price Member" //7
        Constant.PROMO_TYPE_DISCOUNT_MEMBER -> "Promo Discount Member" //8
        Constant.PROMO_TYPE_FREE_MEMBER -> "Promo Free Item" //9
        11 -> "Promo Voucher Special Price"
        Constant.PROMO_TYPE_DISCOUNT_VOUCHER -> "Promo Voucher Discount" //12
        Constant.PROMO_TYPE_FREE_VOUCHER -> "Promo Voucher Free" //13
        Constant.PROMO_TYPE_DEALS -> "Deals" //15
        else -> default
    }
}

fun Context.deviceInfo(): String {
    val config = this.resources.configuration
    val data = hashMapOf(
        "name" to getDeviceName(),
        "model" to Build.MODEL,
        "device" to Build.DEVICE,
        "manufacture" to Build.MANUFACTURER,
        "board" to Build.BOARD,
        "brand" to Build.BRAND,
        "os" to Build.VERSION.SDK_INT,
//        "soc" to Build.SOC_MODEL,
        "smallest_screen_width" to config.smallestScreenWidthDp,
        "architecture" to System.getProperty("os.arch")
    )

    return Gson().toJson(data)
}

inline fun <reified T> Gson.fromJson(json: String): T =
    this.fromJson(json, object : TypeToken<T>() {}.type)