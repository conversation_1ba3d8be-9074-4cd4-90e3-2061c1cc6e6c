package com.uniq.uniqpos.util.view


import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.bugsnag.android.Bugsnag
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.databinding.DialogAuthorizationBinding
import com.uniq.uniqpos.databinding.ListItemAuthDialogBinding
import com.uniq.uniqpos.model.RoleMobile
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import timber.log.Timber

/**
 * Created by annas<PERSON><PERSON><PERSON> on 25/05/18
 */

class DialogAuthorization(mContext: Context) : AlertDialog(mContext) {

    private var authType: AuthType? = null
    private lateinit var binding: DialogAuthorizationBinding

    //    private val employeeList = ArrayList<Employee>()
    private val employeeAuthorized = ArrayList<Employee>()
    private var selectedEmployee = -1
    private var authListener: (Employee) -> Unit = {}
    private var isAutoCheck = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_authorization, null, false)
        setContentView(binding.root)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)

        val employeeList = context.employeeList()
        employeeList.forEach { employee ->
            val role = try {
                Gson().fromJson(employee.roleMobile?.replace("[]", "{}")
                        ?: "{}", RoleMobile::class.java)
            } catch (e: Exception) {
                RoleMobile()
            }

            if (checkPermission(role.authorization ?: RoleMobile())) {
                employeeAuthorized.add(employee.simplify())
            }
        }

        if (employeeList.isEmpty()) {
            binding.txtErr.text = "No Employee List in database"
        } else if (employeeAuthorized.isEmpty()) {
            context.toast("No authorized employee! Auth Type : $authType")
            context.getString(R.string.no_employee_authorized)
            binding.txtErr.text = context.getString(R.string.no_employee_authorized, authType?.name)
//            employeeAuthorized.addAll(employeeList)
        }

        context.employee()?.let { currentEmployee ->
            employeeAuthorized.forEachIndexed { index, employee ->
                if (employee.employeeId == currentEmployee.employeeId) {
                    selectedEmployee = index
                    setEmployeeName(employee)
                }
            }
        }

        //if only one employee, auto-select
        if(employeeAuthorized.size == 1){
            selectedEmployee = 0
            setEmployeeName(employeeAuthorized[0])
        }

        binding.recyclerView.adapter = object : GlobalAdapter<ListItemAuthDialogBinding>(R.layout.list_item_auth_dialog, employeeAuthorized) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemAuthDialogBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                holder.binding.imgCheck.visibility = if (selectedEmployee == position) View.VISIBLE else View.INVISIBLE
                holder.binding.layoutRoot.setBackgroundColor(if (selectedEmployee == position) Color.parseColor("#1F262F") else ContextCompat.getColor(context, R.color.background))
            }
        }

        binding.recyclerView.post { binding.recyclerView.smoothScrollToPosition(employeeAuthorized.size) }
        binding.edtPin.requestFocus()

        binding.recyclerView.addOnItemTouchListener(RecyclerItemClickListener(context) { _, position ->
            val tmpSelected = selectedEmployee
            selectedEmployee = position
            binding.recyclerView.adapter?.notifyItemChanged(tmpSelected)
            binding.recyclerView.adapter?.notifyItemChanged(selectedEmployee)
            setEmployeeName(employeeAuthorized[position])
        })

        binding.btnAuthorize.setOnClickListener { authorize() }
        binding.edtPin.simpleOnTextChanged { if (it.length == 5) authorize() }
    }

    private fun setEmployeeName(employee: Employee) {
        binding.txtName.text = " ${employee.name}"
    }

    private fun authorize() {
        if (selectedEmployee < 0) {
            context.toast("Choose employee")
            return
        }

        if (!Utils.isValidField(binding.edtPin)) return

        if (employeeAuthorized[selectedEmployee].pin != binding.edtPin.value()) {
            context.showMessage("Invalid PIN")
            return
        }

        var roleMobile = employeeAuthorized[selectedEmployee].roleMobile
        Bugsnag.leaveBreadcrumb("DialogAuth. Role : $roleMobile")
        if (roleMobile?.contains("\"authorization\": []") == true) {
            roleMobile = roleMobile.replace("\"authorization\": []", " \"authorization\": {}")
        } else if (roleMobile?.contains("\"authorization\":[]") == true) {
            roleMobile = roleMobile.replace("\"authorization\":[]", " \"authorization\":{}")
        }

        val role = try{
            Gson().fromJson(roleMobile, RoleMobile::class.java)
        }catch (e: Exception){
            context.toast("failed parsing your role!")
            RoleMobile()
        }
        val isAuthorized = checkPermission(role.authorization ?: RoleMobile())
        if (isAuthorized) {
            dismiss()
            authListener(employeeAuthorized[selectedEmployee])
        } else {
            context.showMessage("${employeeAuthorized[selectedEmployee].name} has no permission to $authType")
        }
    }

    private fun checkPermission(role: RoleMobile, isAuth: Boolean = false) = when (authType) {
        AuthType.REFUND -> role.refund
        AuthType.CLOSE_SHIFT -> role.closeshift
        AuthType.REPRINT_CLOSE_SHIFT -> role.reprintCloseShift
        AuthType.REPRINT_RECEIPT -> role.reprintNota || role.reprintReceipt
        AuthType.REPRINT_ORDER -> role.reprintOrder
        AuthType.VOID -> role.void
        AuthType.DISCOUNT_BILL -> role.diskonAll
        AuthType.DISCOUNT_ITEM -> role.diskonperitem
        AuthType.AUTH_FREE -> if (isAuth) role.free else role.freeitem
        AuthType.SALE -> true
        else -> false
    }

    fun setAuthType(authType: AuthType): DialogAuthorization {
        this.authType = authType
        return this
    }

    fun setOnAuthorizedListener(authListener: (Employee) -> Unit): DialogAuthorization {
        this.authListener = authListener
        return this
    }

    fun setAutoCheck(isAutoCheck: Boolean): DialogAuthorization {
        this.isAutoCheck = isAutoCheck
        return this
    }

    override fun show() {
        authType?.let {
            Timber.i("[REQUEST AUTH] for $it")
            val role = context.role()
            if (isAutoCheck && checkPermission(role)) {
                authListener(context.employee() ?: Employee())
            } else {
                super.show()
//                if (checkPermission(context.outletFeature().authFeature, true)) {
//                    super.show()
//                } else {
//                    context.showMessage(context.getString(R.string.feature_disable), "WARNING")
//                }
            }
        } ?: kotlin.run {
            Timber.i("Please Specify Auth Type!")
        }
    }

    enum class AuthType {
        REFUND, CLOSE_SHIFT, REPRINT_CLOSE_SHIFT, REPRINT_RECEIPT, REPRINT_ORDER, VOID, DISCOUNT_BILL,
        DISCOUNT_ITEM, AUTH_FREE, SALE
    }
}