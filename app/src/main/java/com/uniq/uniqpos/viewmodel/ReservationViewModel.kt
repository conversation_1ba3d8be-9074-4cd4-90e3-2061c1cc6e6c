package com.uniq.uniqpos.viewmodel

import androidx.lifecycle.ViewModel
import com.uniq.uniqpos.data.local.entity.ReservationEntity
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.util.SingleLiveEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Created by annasblackhat on 29/05/18
 */
class ReservationViewModel @Inject constructor(private val salesRepository: SalesRepository): ViewModel() {

    private val viewModelJob = Job()
    private val uiScope = CoroutineScope(Dispatchers.Main + viewModelJob)
    val saveTaskCommand = SingleLiveEvent<Void>()

    fun getReservation(outletId: Int?) = salesRepository.getReservations(outletId)

    fun saveReservation(reservationEntity: ReservationEntity) {
        uiScope.launch {
            salesRepository.saveReservation(reservationEntity)
            saveTaskCommand.call()
        }
    }
}