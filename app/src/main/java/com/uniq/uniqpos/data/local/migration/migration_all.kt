package com.uniq.uniqpos.data.local.migration

import androidx.sqlite.db.SupportSQLiteDatabase
import androidx.room.migration.Migration

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 01/02/18.
 */

fun MIGRATION_1_2(): Migration {
    return object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL("ALTER TABLE `sales` ADD COLUMN `displayNota` TEXT NOT NULL DEFAULT ''")
        }
    }
}

fun MIGRATION_2_3(): Migration {
    return object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            if (database.isOpen)
                database.execSQL("ALTER TABLE `employee` ADD COLUMN `pin` TEXT DEFAULT NULL")
        }
    }
}

fun MIGRATION_3_4(): Migration {
    return object : Migration(3, 4) {
        override fun migrate(database: SupportSQLiteDatabase) {
            if (database.isOpen)
                database.execSQL("CREATE TABLE reservation (reservationId INTEGER PRIMARY KEY NOT NULL, timeModified INTEGER, phone TEXT, timeStart INTEGER, name TEXT, timeCreated INTEGER, timeEnd INTEGER, outletFkid INTEGER, synced INTEGER)")
        }

    }
}

/*
fun MIGRATION_4_5(): Migration{
    return object : Migration(4, 5){
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL("ALTER TABLE product ADD `sync` INTEGER NOT NULL DEFAULT `1`")
        }

    }
}*/

fun MIGRATION_19_20() = object : Migration(19, 20) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE member ADD COLUMN typeId INTEGER NOT NULL DEFAULT 0")
        database.execSQL("UPDATE last_sync SET lastSync=0 WHERE tableName = 'MemberEntity'")
    }
}

fun MIGRATION_20_21() = object : Migration(20,21){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE order_sales ADD COLUMN member varchar DEFAULT NULL")
    }
}

fun MIGRATION_21_22() = object : Migration(21,22){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE promotion ADD COLUMN maxQtyPromo int DEFAULT NULL")
    }
}

fun MIGRATION_22_23() = object : Migration(21,22){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE promotion ADD COLUMN maximumRedeemPeriod int DEFAULT NULL")
    }
}

fun MIGRATION_23_24() = object: Migration(23, 24){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("DROP TABLE order_sales")
        database.execSQL("CREATE TABLE `order_sales` (`orderSalesId` TEXT NOT NULL, `timeOrder` INTEGER NOT NULL, `rejectReason` TEXT, `timeTaken` INTEGER, `timeModified` INTEGER NOT NULL, `timeReady` INTEGER, `timeAcceptReject` INTEGER, `pickupTime` TEXT, `salesFkid` TEXT, `items` TEXT NOT NULL, `orderType` TEXT NOT NULL, `outletFkid` INTEGER NOT NULL, `status` TEXT NOT NULL, `member` TEXT, `synced` INTEGER NOT NULL, PRIMARY KEY(`orderSalesId`))")
    }
}

fun MIGRATION_24_25() = object : Migration(24, 25) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL(
            "CREATE TABLE IF NOT EXISTS `sales_tag` " +
                    "(`salesTagId` INTEGER PRIMARY KEY NOT NULL, " +
                    "`adminFkid` INTEGER NOT NULL, " +
                    "`dataStatus` TEXT NOT NULL, " +
                    "`dateCreated` INTEGER NOT NULL, " +
                    "`dateModified` INTEGER NOT NULL, " +
                    "`name` TEXT NOT NULL)"
        )
        database.execSQL("ALTER TABLE sales ADD COLUMN salesTag TEXT DEFAULT NULL")
    }
}

fun MIGRATION_25_26() = object : Migration(25, 26){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE promotion ADD COLUMN termsMultipleApply int DEFAULT NULL")
        database.execSQL("ALTER TABLE cash_recap ADD COLUMN outletCommission int DEFAULT NULL")
    }
}

fun MIGRATION_26_27() = object : Migration(26, 27){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("""
            CREATE TABLE kitchen_display (
                setting_kitchen_display_id INTEGER NOT NULL PRIMARY KEY,
                address TEXT NOT NULL,
                categories TEXT  NOT NULL,
                data_created INTEGER NOT NULL,
                data_modified INTEGER NOT NULL,
                name TEXT  NOT NULL,
                outlet_fkid INTEGER NOT NULL,
                synced INTEGER DEFAULT 0
            )
        """)
    }
}

fun MIGRATION_27_28() = object : Migration(27, 28){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE sales ADD COLUMN salesRefund TEXT DEFAULT NULL")
        database.execSQL("ALTER TABLE refund ADD COLUMN timeCreated INTEGER NOT NULL DEFAULT 0")
    }
}

fun MIGRATION_28_29() = object : Migration(28, 29){
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE sales ADD COLUMN note TEXT DEFAULT NULL")
    }
}

/**
 * Migration from database version 29 to 30.
 * Adds a nullable 'position' column to the 'sub_categories' table.
 */
fun MIGRATION_29_30() = object : Migration(29, 30) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // Adds the new 'position' column to the 'sub_categories' table.
        // The column type is INTEGER for an Int.
        // DEFAULT NULL makes the column nullable.
        // IMPORTANT: Replace 'sub_categories' if your table has a different name!
        database.execSQL("ALTER TABLE subcategory ADD COLUMN position INTEGER DEFAULT NULL")
    }
}