package com.uniq.uniqpos.data.remote.repository

import androidx.lifecycle.LiveData
import com.uniq.uniqpos.data.local.dao.LastSyncDao
import com.uniq.uniqpos.data.local.dao.PromotionDao
import com.uniq.uniqpos.data.local.entity.LastSyncEntity
import com.uniq.uniqpos.data.local.entity.PromotionEntity
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.PromotionUsageResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.service.PromotionService
import com.uniq.uniqpos.util.*
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * Created by annasblackhat on 06/05/19
 */
class PromotionRepository @Inject
constructor(private val promotionDao: PromotionDao,
            private val promotionService: PromotionService,
            private val lastSyncDao: LastSyncDao) {

    fun syncPromotion(outletId: Int){
        var sync = lastSyncDao.getLastSync(PromotionEntity::class.java.simpleName)
        promotionService.getPromotion(outletId, sync?.lastSync ?: 0).awaitListBase { response ->
            response.data?.let { promotionDao.savePromotions(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(PromotionEntity::class.java.simpleName, response.millis))
        }
    }

    fun getActivePromotionLive(outletId: Int): LiveData<Resource<List<PromotionEntity>>> {
        return object : NetworkBoundResource<List<PromotionEntity>, ServerResponseList<PromotionEntity>>() {
            override fun saveCallResult(item: ServerResponseList<PromotionEntity>?) {
                item?.data?.let { data ->
                    promotionDao.savePromotions(data)
                    lastSyncDao.saveLastSync(LastSyncEntity(PromotionEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb(): LiveData<List<PromotionEntity>> {
                val day = SimpleDateFormat("EEEE").format(Date()).toLowerCase()
                val dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK)
                val sunday = if(dayOfWeek == 1) 1 else -1
                val monday = if(dayOfWeek == 2) 1 else -1
                val tuesday = if(dayOfWeek == 3) 1 else -1
                val wednesday = if(dayOfWeek == 4) 1 else -1
                val thursday = if(dayOfWeek == 5) 1 else -1
                val friday = if(dayOfWeek == 6) 1 else -1
                val saturday = if(dayOfWeek == 7) 1 else -1
                val dateNow = System.currentTimeMillis().dateFormat("yyyy-MM-dd")

                Timber.i("Load promotion with dayOfWeek $dayOfWeek ($day) - millis : ${System.currentTimeMillis()} | sun: $sunday, mon: $monday, tue: $tuesday, wed: $wednesday, thu: $thursday, fri: $friday, sat: $saturday ")

                return promotionDao.getActivePromotionLive(dateNow, sunday, monday, tuesday, wednesday, thursday, friday, saturday)
//                return promotionDao.getActivePromotionLive(System.currentTimeMillis(), 1, 1, 1, 1, 1, 1, 1)
//                return promotionDao.getAllPromotionLive()
            }
            override fun createCall(lastSync: Long) = promotionService.getPromotion(outletId, lastSync)
            override fun getLastSync() =  lastSyncDao.getLastSync(PromotionEntity::class.java.simpleName)?.lastSync ?: 0
        }.asLiveData
    }

    fun getPromotionUsage(memberId: Int, promoParentType: String) = promotionService.getPromotionUsage(memberId, promoParentType)

    suspend fun getPromotionUsageAsync(memberId: Int, promoParentType: String): List<PromotionUsageResponse> {
        return suspendCoroutine { continuation ->
            promotionService.getPromotionUsage(memberId, promoParentType).awaitListBase { result ->
                result.takeIf { it.status.safe() }?.data?.let { data ->
                    continuation.resume(data)
                } ?: run {
                    continuation.resumeWithException(WarnException(result.message ?: "failed to get data"))
                }
            }
        }
    }
}