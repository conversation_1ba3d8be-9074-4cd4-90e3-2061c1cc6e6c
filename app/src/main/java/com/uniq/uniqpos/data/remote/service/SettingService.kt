package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.KitchenDisplayEntity
import com.uniq.uniqpos.data.local.entity.PrinterClosingShiftEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.entity.PrinterTicketEntity
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.model.TransactionConfig
import com.uniq.uniqpos.model.SubscriptionStatus
import retrofit2.Call
import retrofit2.http.*

/**
 * Created by ANNASBlackHat on 11/8/17.
 */
interface SettingService {

    @POST("v1/printer")
    fun addPrinterSetting(@Body printer: PrinterEntity): Call<ServerResponse<Int>>

    @GET("v1/printer/{outletId}/{lastSync}")
    fun getPrinterSetting(@Path("outletId")outletId: Int,
                          @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<PrinterEntity>>

    @HTTP(method = "DELETE", path = "v1/printer", hasBody = true)
    fun deletePrinterSetting(@Body printer: PrinterEntity): Call<ServerResponse<Any>>

    @GET("v1/printer_closingshift/{outletId}/{lastSync}")
    fun getPrinterClosingShift(@Path("outletId")outletId: Int,
                               @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<PrinterClosingShiftEntity>>

    @GET("/v1/printer_ticket/{outletId}/{lastSync}")
    fun getPrinterTicket(@Path("outletId")outletId: Int, @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<PrinterTicketEntity>>

    @GET("/v1/billing/subscription/status/{deviceId}")
    fun getSubscriptionStatus(@Path("deviceId")deviceId: String): Call<ServerResponse<SubscriptionStatus>>

    @POST("/v1/outlet/kitchen-display")
    fun addKitchenDisplay(@Body kitchenDisplayEntity: KitchenDisplayEntity): Call<ServerResponse<Any>>
    
    @GET("/v2/outlet/{outletId}/kitchen-display")
    fun getKitchenDisplay(@Path("outletId")outletId: Int, @Query("last_sync") lastSync: Long): Call<ServerResponseList<KitchenDisplayEntity>>

    @DELETE("v2/outlet/{outletId}/kitchen-display/{id}")
    fun removeKitchenDisplay(@Path("id") id: Int, @Path("outletId")outletId: Int?): Call<ServerResponse<Any>>

    @GET("v1/transaction-config")
    fun getTransactionConfig(): Call<ServerResponse<TransactionConfig>>
}