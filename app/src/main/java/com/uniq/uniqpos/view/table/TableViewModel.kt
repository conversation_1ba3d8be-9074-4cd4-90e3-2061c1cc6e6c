package com.uniq.uniqpos.view.table

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.converter.SalesConverter
import com.uniq.uniqpos.data.local.entity.DiningTableEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.repository.OutletRepository
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.SingleLiveEvent
import com.uniq.uniqpos.util.lifecycle.Event
import kotlinx.coroutines.*
import javax.inject.Inject

/**
 * Created by ANNASBlackHat on 15/01/18.
 */
class TableViewModel @Inject constructor(private val outletRepository: OutletRepository,
                                         private val salesRepository: SalesRepository): ViewModel() {

    val saleList = ArrayList<SalesEntity>()

    val taskLoadTable = MutableLiveData<Event<Unit>>()

    fun loadSalesCart(outletId: Int){
        viewModelScope.launch(Dispatchers.IO) {
            val cart = salesRepository.getTmpSalesLocal(outletId)
            saleList.clear()
            cart.forEach { c -> saleList.add(SalesConverter().fromStringToObject(c.sales)) }
            taskLoadTable.postValue(Event(Unit))
        }
    }

    fun getDiningTable(outletId: Int) = outletRepository.getDiningTableSync(outletId)

    fun getTransactionCartLive(outletId: Int) = salesRepository.getSalesCart(outletId)

    fun addTable(tableName: String, outlet: Outlet?){
        val table = DiningTableEntity()
        table.diningTableId = System.currentTimeMillis()
        table.tableName = tableName.trim()
        table.outletFkid = outlet?.outletId ?: 0
        table.status = Constant.TABLE_STATUS_EMPTY
        viewModelScope.launch { outletRepository.addOrUpdateTable(table) }
    }

    fun updateTableStatus(diningTableEntity: DiningTableEntity) {
        viewModelScope.launch { outletRepository.updateTableStatus(diningTableEntity) }
    }

    fun syncTmpSales(outletId: Int?){
        viewModelScope.launch(Dispatchers.IO) {
            salesRepository.syncTempSales(outletId)
        }
    }

    fun deleteTable(diningTable: DiningTableEntity) {
        viewModelScope.launch { outletRepository.removeTable(diningTable) }
    }

    fun updateTable(diningTable: DiningTableEntity) {
        viewModelScope.launch { outletRepository.addOrUpdateTable(diningTable) }
    }

}