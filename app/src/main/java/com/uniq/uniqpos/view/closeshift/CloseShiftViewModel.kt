package com.uniq.uniqpos.view.closeshift

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.OperationalCost
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.data.remote.repository.OutletRepository
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.CashRecapData
import com.uniq.uniqpos.model.CashRecapDetail
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.collections.HashMap
import kotlin.random.Random

/**
 * Created by ANNASBlackHat on 22/10/2017.
 */

class CloseShiftViewModel @Inject
constructor(
    private val salesRepository: SalesRepository,
    private val productRepository: ProductRepository,
    private val settingRepository: SettingRepository,
    private val outletRepository: OutletRepository,
    private val sharedPref: SharedPref
) : ViewModel() {

    var printerSettingCloseShift: PrinterClosingShiftEntity? = null
    val printerCloseShift = ArrayList<PrinterEntity>()
    val printerCloseShiftSetting = ArrayList<PrinterClosingShiftEntity>()
    var suggestionToPrintRecap = false
    val cashRecapList = ArrayList<CashRecapEntity>()
    val openShiftList = ArrayList<OpenShiftEntity>()
    val shiftList = ArrayList<ShiftEntity>()
    private val cacheReport = HashMap<String, String>() //key: cash recap id (can multiple)
    private val cacheCashRecapData =
        HashMap<String, CashRecapData>() //key: open shift id (can multiple)

    var closeShiftTask = SingleLiveEvent<Boolean>()
    val pDialogTask = SingleLiveEvent<String?>()

    private val _dialogMessageTask = MutableLiveData<Event<String>>()
    val dialogMessageTask: LiveData<Event<String>> = _dialogMessageTask

    val refreshCashRecap = MutableLiveData<Event<Unit>>()
    val taskShowRecapDetail = SingleLiveEvent<CashRecapDetail>()
    val taskLogOut = SingleLiveEvent<Void>()
    val taskPrint = SingleLiveEvent<ArrayList<PendingPrintEntity>>()
    val taskCashRecap = SingleLiveEvent<CashRecapData>()

    private val _warning = MutableLiveData<Event<String>>()
    val warning: LiveData<Event<String>> = _warning

    val reservedCloseShiftId = System.currentTimeMillis() //set the id earlier, to prevent duplicate data

    fun getTotalTransactionByShift(openShiftId: Long) =
        salesRepository.totalSalesByShift(openShiftId)

    fun getPrinterSetting() = settingRepository.getPrinters()
    fun getCashRecap() = salesRepository.getCashRecap()
    fun getOpenShift(outletId: Int) = outletRepository.getOpenShiftSync(outletId)

    fun getCashRecapData(
        func: (List<SalesEntity>, ArrayList<OperationalCost>, List<TmpSalesEntity>, List<SubCategoryEntity>) -> Unit,
        vararg openShiftIds: Long
    ) {
        //if we have it in cache, then use it
        cacheCashRecapData[openShiftIds.joinToString()]?.let { cashRecapData ->
            Timber.i("[CLOSE] getCashRecapData use cache...")
            func(
                cashRecapData.sales,
                cashRecapData.operationalCost,
                cashRecapData.tmpSales,
                cashRecapData.subcategory
            )
            return
        }

        Timber.i("[CLOSE] getCashRecapData generate new...")
        val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        Timber.i("Open Shift Ids : ${Gson().toJson(openShiftIds)} | ${openShiftIds.joinToString(",")}")
        viewModelScope.launch {
            Timber.i("Get sales recap...")
//          val sales = salesRepository.getSalesRecap(*openShiftIds)
            val sales = withContext(Dispatchers.IO) { salesRepository.getSalesRecap(*openShiftIds) }
            Timber.i("Sales Size : ${sales.size} \n-->Get Operational Cost...")
            val op = salesRepository.getOperationalCost(*openShiftIds)
            Timber.i("Operational Cost size : ${op.size} \n-->Get TmpSales...")
            val pending = salesRepository.getTmpSalesLocal(outlet?.outletId)
            Timber.i("TmpSales Size : ${pending.size} \n-->Get sub category...")
            val category = productRepository.getSubCategories()

            //save to our cache
            cacheCashRecapData[openShiftIds.joinToString()] =
                CashRecapData(sales, op, pending, category, emptyList(), null)

            func(sales, op, pending, category)
        }
    }

    private suspend fun getCashRecapDataAsync(vararg openShiftIds: Long): CashRecapData {
        //if we have it in cache, then use it
        cacheCashRecapData[openShiftIds.joinToString()]?.let { cashRecapData ->
            Timber.i("[CLOSE] getCashRecapData use cache...")
            return cashRecapData
        }

        Timber.i("[CLOSE] getCashRecapData generate new...")
        val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        Timber.i("Open Shift Ids : ${Gson().toJson(openShiftIds)} | ${openShiftIds.joinToString(",")}")

        Timber.i("Get sales recap...")
//          val sales = salesRepository.getSalesRecap(*openShiftIds)
        val sales = withContext(Dispatchers.IO) { salesRepository.getSalesRecap(*openShiftIds) }
        Timber.i("Sales Size : ${sales.size} \n-->Get Operational Cost...")
        val op = salesRepository.getOperationalCost(*openShiftIds)
        Timber.i("Operational Cost size : ${op.size} \n-->Get TmpSales...")
        val pending = salesRepository.getTmpSalesLocal(outlet?.outletId)
        Timber.i("TmpSales Size : ${pending.size} \n-->Get sub category...")
        val category = productRepository.getSubCategories()

        var startTime = System.currentTimeMillis()
        var endTime = 0.toLong()
        salesRepository.getOpenShift(*openShiftIds).forEach {
            if (it.timeOpen < startTime) startTime = it.timeOpen
            if (it.timeClose.safe() > endTime) endTime = it.timeClose.safe()
        }

        val totalPiutang = salesRepository.getTotalPiutangPayment(startTime, endTime)
        Timber.i("totalPiutang, from $startTime to $endTime : $totalPiutang")

        val debtList = salesRepository.getDebtPayment(startTime, endTime)
        Timber.i("debtHistory, from $startTime to $endTime : ${debtList.size} data")
        Timber.d("debtHistory -> ${Gson().toJson(debtList)}")

        //save to our cache
        val result = CashRecapData(sales, op, pending, category, emptyList(), null, debtList)
        cacheCashRecapData[openShiftIds.joinToString()] = result
        return result
    }

    fun initCloseShiftPrinter() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val printers =
                    settingRepository.getPrinters().filter { it.settingClosingshift == "1" }
                val printerClosing = settingRepository.getPrintersClosing()

//                val basicRule = RolePrinterClosing(itemsales = true, groupsales = true, tax = true, actual = true, avgpaxbill = true, paymentmedia = true)
                val basicRule = PrintNotaUtil.getDefaultPrintCloseShiftSetting()
                printerSettingCloseShift =
                    printerClosing.firstOrNull { it.printerSettingFkid == printers.firstOrNull()?.printerSettingId }
                        ?: PrinterClosingShiftEntity("", rules = Gson().toJson(basicRule))

                printerCloseShift.clear()
                printerCloseShift.addAll(printers)

                printerCloseShiftSetting.clear()
                printerCloseShiftSetting.addAll(printerClosing)
            }
        }
    }

    fun buildSuggestionPrintRecap(data: ShiftOpen?) {
        data?.let { shiftOpen ->
            viewModelScope.launch {
                withContext(Dispatchers.IO) {
                    val shiftList = outletRepository.getAllShift()
                    val closedShift =
                        salesRepository.countTotalClosedShift(Utils.getMinTimeMillisToday())

                    suggestionToPrintRecap =
                        if (shiftList.size > 1 && shiftOpen.timeOpen.dateFormat() != System.currentTimeMillis()
                                .dateFormat()
                        ) {
                            Timber.i(
                                "Suggestion : YES, because : now (${
                                    System.currentTimeMillis().dateFormat()
                                }) not same with open shift (${shiftOpen.timeOpen.dateFormat()})"
                            )
                            true
                        } else {
                            closedShift >= (shiftList.size - 1)
                        }
                    Timber.i("Suggestion : $suggestionToPrintRecap, closed shift -> $closedShift & total shift -> ${shiftList.size}")
                }
            }
        }
    }

    fun saveCloseShift(cashRecap: CashRecapEntity, isPrintDailyRecap: Boolean) {
        viewModelScope.launch {
            try {
                pDialogTask.postValue("process...")

                val deviceId = sharedPref.getString(SharedPref.DEVICE_ID, "").safe()
                val offlineMode = false ///context?.outletFeature()?.offlineMode ?: false

                //check active device
                Timber.i("[close shift] check active device... current device id: $deviceId")
                val activeDevices =
                    salesRepository.getActiveDevice(cashRecap.openShiftId, deviceId).await()
                activeDevices.data?.takeIf { it.isNotEmpty() }?.let { data ->
                    val devices = data.map { it.name }.joinToString()
                    throw WarnException("Beberapa Device berikut Harus logout terlebih dahulu : \n$devices")
                }

                //sync sales
                Timber.i("[close shift] sync sales...")
                pDialogTask.postValue("sync data...")
                launch(Dispatchers.IO) {
                    salesRepository.syncSales(cashRecap.outletId, deviceId)
                    salesRepository.validateSales(cashRecap.openShiftId, cashRecap.outletId.safe())
                }.join()

                //submit close shift data
                Timber.i("[close shift] submit close shift data...")
                pDialogTask.postValue("saving...")
                val isSuccess = salesRepository.saveCloseShift(cashRecap, offlineMode)
//                closeShiftTask.postValue(res)

                //update current openshift (timeClose)
                val shift = sharedPref.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
                salesRepository.updateOpenShift(shift?.openShiftId.safe(), System.currentTimeMillis())
                Timber.i("[close shift] update shift ${shift?.openShiftId} to ${System.currentTimeMillis()}")

                if (!isSuccess) {
                    throw WarnException("Pastikan koneksi internet anda stabil!")
                }

                //if close shift successfully submitted
                //clear user session immediately
                sharedPref.putData(SharedPref.LOGIN_EMPLOYEE_STATUS, false)

                if (printerCloseShift.isNotEmpty()) {
                    pDialogTask.postValue("printing...")

                    val recapData = getCashRecapDataAsync(cashRecap.openShiftId)

                    val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
                        ?: Outlet()
                    val employee =
                        sharedPref.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java)
                            ?: Employee()
                    val currentShift =
                        sharedPref.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
                            ?: ShiftOpen()

                    val openShift = OpenShiftEntity(
                        currentShift.openShiftId,
                        currentShift.timeOpen,
                        currentShift.employeeFkid,
                        currentShift.shiftFkid,
                        currentShift.timeClose,
                        0,
                        currentShift.earlyCash,
                        currentShift.outletFkid
                    )
                    val printList = ArrayList<PendingPrintEntity>()

                    val allShifts =
                        openShiftList.filter { it.timeOpen.dateFormat() == currentShift.timeOpen.dateFormat() }
                    val recapDataDaily =
                        if (isPrintDailyRecap) getCashRecapDataAsync(*allShifts.map { it.openShiftId }
                            .toLongArray()) else null
                    val ids = allShifts.map { it.openShiftId }
                    val cashRecaps = cashRecapList.filter { cr -> ids.any { it == cr.openShiftId } }
                    if (isPrintDailyRecap) Timber.i("all openShift ids: $ids - open at: ${currentShift.timeOpen.dateFormat()} - cashRecapIds: ${cashRecaps.map { it.id }}")

                    val startDate: Long = allShifts.firstOrNull()?.timeOpen ?: 0
                    val endDate: Long = allShifts.lastOrNull()?.timeClose ?: 0
                    val debtList = salesRepository.getDebtPayment(startDate, endDate)
                    Timber.i("debtHistory, from $startDate to $endDate : ${debtList.size} data")
                    Timber.d("debtHistory -> ${Gson().toJson(debtList)}")

                    printerCloseShift.forEach { printer ->
                        Timber.i("print close shift to ${printer.address} (${printer.type})")

                        val printerClosingSetting = ArrayList<PrinterClosingShiftEntity>()
                        printerClosingSetting.addAll(printerCloseShiftSetting.filter { it.printerSettingFkid == printer.printerSettingId })

                        if (printerClosingSetting.isEmpty()) {
                            Timber.i("no closing shift ticket for printer ${printer.address}, create default")
                            printerClosingSetting.add(
                                PrinterClosingShiftEntity(
                                    "",
                                    rules = Gson().toJson(PrintNotaUtil.getDefaultPrintCloseShiftSetting())
                                )
                            )
                        }

                        printerClosingSetting.forEach { setting ->
                            Timber.i("generating close shift ticket : '${setting.name}'")
                            var printFormat = PrintNotaUtil.getCashRecapFormat(
                                arrayListOf(cashRecap),
                                outlet,
                                employee,
                                recapData.sales,
                                recapData.tmpSales,
                                recapData.operationalCost,
                                setting,
                                recapData.subcategory,
                                {},
                                printer.settingPrintpapersize,
                                null,
                                openShift,
                                isReprint = false,
                                debtPaymentList = recapData.debtList
                            ) + " ${Constant.PRINTER_CODE_CUT} "
                            printList.add(
                                PendingPrintEntity(
                                    printer.address,
                                    printer.type,
                                    printFormat,
                                    printer.name,
                                    System.currentTimeMillis() + Random.nextInt(1000)
                                )
                            )

                            recapDataDaily?.takeIf { isPrintDailyRecap }?.apply {
                                printFormat = PrintNotaUtil.getCashRecapFormat(
                                    cashRecaps,
                                    outlet,
                                    employee,
                                    sales,
                                    tmpSales,
                                    operationalCost,
                                    setting,
                                    subcategory,
                                    {},
                                    printer.settingPrintpapersize,
                                    "#DAILY RECAP  : ${currentShift.timeOpen.dateFormat()}",
                                    *this.openShifts.toTypedArray(),
                                    isRecap = true,
                                    debtPaymentList = debtList
                                ) + " ${Constant.PRINTER_CODE_CUT} "
                                printList.add(
                                    PendingPrintEntity(
                                        printer.address,
                                        printer.type,
                                        printFormat,
                                        printer.name,
                                        System.currentTimeMillis() + Random.nextInt(1000)
                                    )
                                )
                            }
                        }
                    }

                    //task to print...
                    taskPrint.postValue(printList)
                } else {
                    Timber.i("no printer for closing shift...")
                    taskLogOut.call()
                }
            } catch (e: Exception) {
                _dialogMessageTask.postValue(Event(e.readableError()))
                pDialogTask.call()
            }
        }
    }

    private fun generateCloseShiftReport(
        data: CashRecapData,
        isAutoPrint: Boolean = false,
        isRecap: Boolean = false,
        debtList: List<PiutangHistoryEntity> = emptyList(),
        func: (String) -> Unit
    ): String {
        val ids = data.openShifts.map { it.openShiftId }
        cacheReport[ids.joinToString()]?.let { report ->
            Timber.i("[CLOSE] report using cache..")
            func(report)
            return report
        }

        val cashRecaps = cashRecapList.filter { cr -> ids.any { it == cr.openShiftId } }
        val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        val employee = sharedPref.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java)
        if (outlet == null || employee == null) {
            _dialogMessageTask.postValue(Event("system failed to get some data, consider to re-login"))
            return ""
        }

        Timber.i("close-report generate new.. from openShiftIds: $ids | ${data.sales.size} sales")

        //if generating for close shift, use paper size from setting, otherwise force to use 58 size (in order to beautify the UI)
        val paperSize =
            if (isAutoPrint) printerCloseShift.firstOrNull()?.settingPrintpapersize else 58
        return PrintNotaUtil.getCashRecapFormat(
            cashRecaps,
            outlet,
            employee,
            data.sales,
            data.tmpSales,
            data.operationalCost,
            printerSettingCloseShift,
            data.subcategory,
            { result ->
                cacheReport[ids.joinToString()] = result
                func(result)
            },
            paperSize,
            data.watermark,
            *data.openShifts.toTypedArray(),
            isRecap = isRecap,
            debtPaymentList = debtList
        )
    }

    fun loadShift() {
        viewModelScope.launch {
            if (shiftList.isEmpty()) {
                val shift = outletRepository.getAllShift()
                shiftList.clear()
                shiftList.addAll(shift)
            }
        }
    }

    fun initCloseShiftData() {
        viewModelScope.launch {
            if (shiftList.isEmpty()) {
                val shift = outletRepository.getAllShift()
                shiftList.clear()
                shiftList.addAll(shift)
            }

            cashRecapList.forEach { cashRecap ->
                openShiftList.firstOrNull { it.openShiftId == cashRecap.openShiftId }
                    ?.takeIf { os -> shiftList.any { it.shiftId == os.shiftFkid } }
                    ?.let { openShift ->
                        cashRecap.shiftName =
                            shiftList.first { it.shiftId == openShift.shiftFkid }.name
                    }
            }
            refreshCashRecap.value = Event(Unit)
        }
    }

    fun getCloseShiftReport(cashRecap: CashRecapEntity) {
        Timber.i("generate closeShiftReport, id: ${cashRecap.id} | OpenShiftSize : ${openShiftList.size} | openShift : ${cashRecap.openShiftId}")
        viewModelScope.launch {
            pDialogTask.postValue("loading...")
            val employee = sharedPref.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java)
                ?: Employee()
            val openShift = openShiftList.first { it.openShiftId == cashRecap.openShiftId }
            val watermark = "#REPRINT BY   : ${employee.name}\n" +
                    "#REPRINT DATE : ${Date().dateTimeFormat()}"

            val cashRecapData = getCashRecapDataAsync(openShift.openShiftId)
            val debtList = salesRepository.getDebtPayment(openShift.timeOpen, openShift.timeClose.safe())
            Timber.i("debtHistory, from ${openShift.timeOpen} to ${openShift.timeClose} : ${debtList.size} data")
            Timber.d("debtHistory -> ${Gson().toJson(debtList)}")

            val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
            val paperSize = printerCloseShift.firstOrNull()?.settingPrintpapersize.safe(58)
            val printFormat = PrintNotaUtil.getCashRecapFormat(
                arrayListOf(cashRecap),
                outlet,
                employee,
                cashRecapData.sales,
                cashRecapData.tmpSales,
                cashRecapData.operationalCost,
                printerSettingCloseShift,
                cashRecapData.subcategory,
                { },
                paperSize,
                watermark,
                openShift,
                debtPaymentList = debtList
            )

            pDialogTask.postValue(null)
            taskShowRecapDetail.postValue(
                CashRecapDetail(
                    printFormat,
                    cashRecapData,
                    cashRecapList,
                    listOf(openShift.openShiftId)
                )
            )
        }
    }

    fun generateDailyRecap(date: String) {
        Timber.i("generating daily recap: $date")
        val openShifts = openShiftList.filter { it.timeOpen.dateFormat() == date }
        if (openShifts.isEmpty()) {
            _dialogMessageTask.postValue(Event("tidak ada shift ditemukan buka pada tanggal $date. Pastikan anda menggunakan tanggal buka shift"))
            return
        }

        viewModelScope.launch {
            pDialogTask.postValue("loading...")
            val cashRecap = getCashRecapDataAsync(*openShifts.map { it.openShiftId }.toLongArray())

            val shiftIds = openShifts.map { it.shiftFkid }
            val shiftNames =
                shiftList.filter { shift -> shiftIds.any { id -> id == shift.shiftId } }
                    .map { it.name }.joinToString()

            cashRecap.watermark = "#DAILY RECAP : $date\n" +
                    "#REPRINT AT : ${Date().dateTimeFormat()}\n"

            if (shiftNames.isNotEmpty()) {
                cashRecap.watermark += "#SHIFT  : $shiftNames"
            }

            cashRecap.openShifts = openShifts

            var startDate: Long = 0
            var endDate: Long = System.currentTimeMillis()

            if (cashRecap.openShifts.isNotEmpty()) {
                startDate = cashRecap.openShifts.first().timeOpen
                endDate = cashRecap.openShifts.last().timeClose.safe(default = System.currentTimeMillis())
            }

            val debtHistory = salesRepository.getDebtPayment(startDate, endDate)
            Timber.i("debtHistory, from $startDate to $endDate : ${debtHistory.size} data")
            Timber.d("debtHistory -> ${Gson().toJson(debtHistory)}")

            val printFormat = generateCloseShiftReport(cashRecap, false, isRecap = true, debtList = debtHistory) {}

            val cashRecapFiltered = ArrayList<CashRecapEntity>()
            cashRecap.openShifts.map { it.openShiftId }.forEach { openShiftId ->
                cashRecapList.firstOrNull { it.openShiftId == openShiftId }?.let {
                    cashRecapFiltered.add(it)
                }
            }

            pDialogTask.postValue(null)
            taskShowRecapDetail.postValue(
                CashRecapDetail(
                    printFormat,
                    cashRecap,
                    cashRecapFiltered,
                    openShifts.map { it.openShiftId })
            )
        }
    }

    fun getCashRecapData(openShiftIds: List<Long>) {
        viewModelScope.launch {
            val result = getCashRecapDataAsync(*openShiftIds.toLongArray())
            taskCashRecap.postValue(result)
        }
    }

    fun checkWarning() {
        viewModelScope.launch {
            val lastSyncTime = sharedPref.getString(SharedPref.LAST_SYNC, "0")
            Timber.i("last sync from sp: $lastSyncTime")
            salesRepository.getLastSync()?.let { lastSynced ->
                val diffMinutes =  lastSynced.diffMinute()
                val diffHour = diffMinutes / 60
                val diffHourMinute = diffMinutes % 60
                val diffInfo = if (diffHour > 0) "$diffHour jam, $diffHourMinute menit" else "$diffMinutes menit"
                Timber.i("last sync warn: $diffInfo, lastSyncSales: $lastSynced, lastSyncSharedPred $lastSyncTime")
                if(diffMinutes >= 5 ) {
//                    _warning.postValue(Event("singkronasi terakhir $diffInfo yang lalu"))
                }else if (!_warning.value?.peekContent().isNullOrBlank()){
                    _warning.postValue(Event(""))
                }
            }
        }
    }

}
