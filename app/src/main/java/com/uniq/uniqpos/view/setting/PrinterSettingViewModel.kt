package com.uniq.uniqpos.view.setting

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.PrinterClosingShiftEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.entity.PrinterTicketEntity
import com.uniq.uniqpos.data.local.entity.SubCategoryEntity
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.data.remote.repository.SystemRepository
import com.uniq.uniqpos.model.PrintTicket
import com.uniq.uniqpos.model.RolePrinterClosing
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by ANNASBlackHat on 18/10/2017.
 */
class PrinterSettingViewModel @Inject constructor(
        private val settingRepository: SettingRepository,
        private val systemRepository: SystemRepository,
        private val productRepository: ProductRepository) : ViewModel() {

    val printerList = ArrayList<PrinterEntity>()
    var printTicketOrder = ArrayList<PrinterTicketEntity>()
    var printTicketCloseShift = ArrayList<PrinterTicketEntity>()
    var subCategories = ArrayList<SubCategoryEntity>()

    var ticketOrderRecView = ArrayList<PrintTicket>()
    var ticketCloseShiftRecView = ArrayList<PrintTicket>()

    val taskRefreshPrinter = SingleLiveEvent<Void>()

    val printers = settingRepository.printers

    fun addPrinter(printerEntity: PrinterEntity) {
        viewModelScope.launch {
            settingRepository.addPrinter(printerEntity)
        }
    }

    fun updatePrinterByAddress(printerEntity: PrinterEntity) {
        viewModelScope.launch {
            settingRepository.updatePrinterByAddress(printerEntity)
        }
    }

    fun syncPrinter(outletId: Int?) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                settingRepository.syncSetting(outletId)
            }
        }
    }

    fun deletePrinter(printerEntity: PrinterEntity, outletId: Int?) {
        viewModelScope.launch { settingRepository.deletePrinter(printerEntity, outletId) }
    }

    fun lookupVendor(data: NetworkScanner.ScannerData) {
        viewModelScope.launch {
            try {
                val vendor = systemRepository.lookupVendor(data.macAddress)
                if (!printerList.any { it.address == data.ipAddress }) {
                    printerList.add(PrinterEntity(data.ipAddress, vendor, Constant.PRINTER_TYPE_WIFI))
                }
            } catch (e: Exception) {
                Timber.i("Get vendor error $e")
                if (!printerList.any { it.address == data.ipAddress }) {
                    printerList.add(PrinterEntity(data.ipAddress, "Unknown Device", Constant.PRINTER_TYPE_WIFI))
                }
            } finally {
                taskRefreshPrinter.call()
            }
        }
    }

    fun loadPrintTicket(id: Int) {
        viewModelScope.launch {
            printTicketOrder.clear()
            printTicketOrder.addAll(settingRepository.getPrintTicketOrderByPrinterId(id))

            subCategories.clear()
            subCategories.addAll(productRepository.getSubCategories())

            ticketOrderRecView.clear()
            printTicketOrder.forEach { ticket ->
                ticketOrderRecView.add(PrintTicket("#" + ticket.name.safe(), true))
                ticket.detail.forEach { detail ->
                    ticketOrderRecView.add(PrintTicket(subCategories.firstOrNull { it.productCategoryId == detail.productSubcategoryFkid }?.name.safe()))
                }
            }

            val printCloseShift = arrayListOf<PrinterClosingShiftEntity>()
            printCloseShift.addAll(settingRepository.getPrinterCloseShiftByPrinterId(id))
//            val defaultCloseShift = listOf("Sales", "Group Sales", "Sales Recap", "Void", "Refund")

            if (printCloseShift.isEmpty()) {
                val defaultSetting = Gson().toJson(PrintNotaUtil.getDefaultPrintCloseShiftSetting())
                printCloseShift.add(PrinterClosingShiftEntity(name = "default", rules = defaultSetting, closingshiftId = ""))
            }

            ticketCloseShiftRecView.clear()
            printCloseShift.forEach { print ->
                val role = try {
                    Gson().fromJson(print.rules, RolePrinterClosing::class.java)
                } catch (e: Exception) {
                    Timber.i("[EXCEPTION] converting with gson failed : $e | role : ${print.rules}")
                    RolePrinterClosing()
                }

                ticketCloseShiftRecView.add(PrintTicket("#" + print.name, true))

                val roleSimply = mapOf("Time" to role.time, "Payment Media" to role.paymentmedia, "Item Sales" to role.itemsales,
                        "Group Sales" to role.groupsales, "Tax" to role.tax, "AVG Pax Bill" to role.avgpaxbill, "FIFO" to role.fifo,
                        "Aktual" to role.actual, "Operational Cost Detail" to role.opcostdetail,
                        "Entertaint Income" to role.entertainincome)
                roleSimply.filter { it.value }.forEach {
                    ticketCloseShiftRecView.add(PrintTicket(it.key))
                }
            }

            taskRefreshPrinter.call()
        }
    }
}