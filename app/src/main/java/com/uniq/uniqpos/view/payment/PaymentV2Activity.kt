package com.uniq.uniqpos.view.payment

import android.app.Activity
import android.app.Application
import android.content.*
import android.content.pm.ActivityInfo
import android.graphics.PorterDuff
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.widget.FrameLayout
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.commit
import androidx.lifecycle.lifecycleScope
import com.bugsnag.android.Bugsnag
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityPaymentV2Binding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.DynamicDialogFragment
import kotlinx.coroutines.*
import timber.log.Timber
import java.lang.ref.WeakReference

class PaymentV2Activity : BaseActivity<PaymentViewModel, ActivityPaymentV2Binding>() {
    override fun getLayoutRes() = R.layout.activity_payment_v2
    override fun getViewModel() = PaymentViewModel::class.java
    override fun getDisplayHomeAsUpEnabled() = true

    var isTabletMode = false

    private val RC_SPLITBILL: Int = 1
    private val RC_PAYMENT: Int = 2

    private val paymentDetailFragment: PaymentDetailFragment get() = PaymentDetailFragment()
    private val paymentDetailDialog: DynamicDialogFragment by lazy {
        DynamicDialogFragment(
            paymentDetailFragment,
            this,
            layoutInflater
        )
    }

    companion object {

        fun setData(sales: SalesEntity) {

        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        super.onCreate(savedInstanceState)

        //force orientation to landscape
        if (resources.getBoolean(R.bool.landscape_only)) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
        }

        handleIntent()
        viewModel.initPaymentList()
        viewModel.loadBankMedia()

        //load container
        if (savedInstanceState == null) {
            Bugsnag.leaveBreadcrumb("load paymentList fragment...")
            supportFragmentManager.commit {
                replace(R.id.container_payment_list, PaymentListFragment())
            }
            findViewById<FrameLayout>(R.id.container_payment_detail)?.let {
                isTabletMode = true
                viewModel.isTabletMode = true
                updatePaymentDetailUi()
                viewModel.refreshPaymentOption()
            }
        }

        Bugsnag.leaveBreadcrumb("init ui...")
        initUi()

        Bugsnag.leaveBreadcrumb("init feature...")
        initViewFeature()

        Bugsnag.leaveBreadcrumb("finish onCreate...")
        Timber.i("finish onCreate...")
        //notify, just for checking
//        val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
//        Bugsnag.notify(Exception("sales-payment-${outlet.name}"))
    }

    private fun finishPayment() {
        viewModel.finishPayment()
    }

    private fun handleIntent() {
//        val paymentFlow = intent.getStringExtra(PaymentActivity.INTENT_PAY_FLOW) ?: ""
        viewModel.isPiutang = intent.getBooleanExtra("ispiutang", false)
        viewModel.receiptReceiver.value = intent.getStringExtra("receipt_receiver")
        viewModel.isOpeningCashDrawer

        Timber.i(
            "intent: sales? ${intent.hasExtra("sales")} | sale-json? ${intent.hasExtra("sales-json")} | sales-json-static? ${
                intent.hasExtra(
                    "sales-json-static"
                )
            }"
        )
        if (intent.hasExtra("sales")) {
            Bugsnag.leaveBreadcrumb("catch intent: sales")
            viewModel.sales = intent.getParcelableExtra("sales") as? SalesEntity ?: SalesEntity("")
        } else if (intent.hasExtra("sales-json-static")) {
            Bugsnag.leaveBreadcrumb("catch intent: sales-json-static")
            DataHolder.getData()?.let { salesJson ->
                viewModel.sales = Gson().fromJson(salesJson, SalesEntity::class.java)
            } ?: run {
                showMessage("no sales data...")
            }
        } else if (intent.hasExtra("sales-json")) {
            Bugsnag.leaveBreadcrumb("catch intent: sales-json")
            intent.getStringExtra("sales-json")?.let { salesJon ->
                viewModel.sales = Gson().fromJson(salesJon, SalesEntity::class.java)
            } ?: kotlin.run {
                showMessage("no sales...")
            }
        } else if (!viewModel.isPiutang) {
            toast("no item on sales", level = Level.ERROR)
            finish()
            return
        }

        Bugsnag.leaveBreadcrumb("finish got sales")
        (intent.getSerializableExtra("merge_ids") as? ArrayList<String>)?.let {
            viewModel.mergeIds.addAll(
                it
            )
        }

        if (viewModel.isPiutang) {
            viewModel.piutang = intent.getParcelableExtra("piutang")
        }

        viewModel.salesOriginal = intent.getParcelableExtra("sales_original") as? SalesEntity
    }

    fun updatePaymentDetailUi() {
        findViewById<FrameLayout>(R.id.container_payment_detail)?.let {
            supportFragmentManager.commit {
                replace(R.id.container_payment_detail, paymentDetailFragment)
            }
        } ?: run {
            dismissPaymentDetailDialog()
            Timber.d("#detail, showing: ${paymentDetailDialog.isShowing} | $paymentDetailDialog")
            paymentDetailDialog.addDialogListener { isShowing ->
                if (!isShowing) {
                    viewModel.taskRefreshPaymentList.postValue(viewModel.selectedPaymentIdx)
                    toast("refresh list...")
                }
            }
            paymentDetailDialog.show(supportFragmentManager, "payment-detail")
            Timber.d("#detail, showing: ${paymentDetailDialog.isShowing} | $paymentDetailDialog")
        }
    }

    fun dismissPaymentDetailDialog() {
        if (paymentDetailDialog.isShowing) {
            paymentDetailDialog.dismiss()
        }
    }

    private fun initUi() {
        binding.viewmodel = viewModel
        binding.layoutFooter.viewmodel = viewModel
        binding.layoutFooter.lifecycleOwner = this

        viewModel.salesOriginal?.let {
            Timber.i("Sales Original : ${Gson().toJson(viewModel.salesOriginal)}")
            binding.layoutHeader.btnSplit.setTextColor(Utils.getColor(this, R.color.text_grey))
            binding.layoutHeader.btnSplit.background.setColorFilter(
                ContextCompat.getColor(
                    this,
                    R.color.background
                ), PorterDuff.Mode.SRC_ATOP
            )
            binding.layoutHeader.btnSplit.isEnabled = false
        }

        binding.layoutHeader.txtGrandTotal.text = viewModel.getGrandTotal().toCurrency()

        binding.layoutFooter.btnFinishPayment.setOnClickListener { finishPayment() }
        binding.layoutHeader.btnSplit.setOnClickListener { splitBill() }
    }

    private fun splitBill(isConfirmed: Boolean = false) {
        if (!viewModel.validateSplitBill()) {
            return
        }

        val discVouch = arrayListOf<String>()
        if (viewModel.sales.discount?.discount.safe() > 0) discVouch.add("Discount")
        if (viewModel.sales.discount?.voucher.safe() > 0) discVouch.add("Voucher")

        if (!isConfirmed && discVouch.isNotEmpty()) {
            showMessage(
                getString(R.string.warning_disvouch_remove, discVouch.joinToString(" & ")),
                getString(R.string.warning),
                { _, _ ->
                    splitBill(true)
                })
            return
        }

        Timber.i("split bill for salesId ${viewModel.sales.noNota}")

        val intent = Intent(this, SplitBillActivity::class.java)
        intent.putExtra("sales", viewModel.sales.copy(discount = null))
        startActivityForResult(intent, RC_SPLITBILL)
    }


    override fun observeTask() {
        super.observeTask()
        setupToastMessage(this, viewModel.toastMessage)
        setupDialogMessage(this, viewModel.dialogMsg)

        viewModel.finishTaskCommand.observe(this) {
            finish()
        }

        viewModel.pDialogTaskCommand.observe(this) { isShow ->
            showDialog(isShow ?: false)
        }

        viewModel.paymentSavedTask.observe(this) { sales ->
            onSalesSaved(sales)
        }

        viewModel.printTask.observe(this) { printTask ->
            printTask?.data?.let { printWifi ->
                Bugsnag.leaveBreadcrumb("printing receipt in payment")
                toast("printing...")
                managePrintWifi(printWifi)
                showDialog(false)
                if (printTask.isExitAfterPrint) {
                    setResult(Activity.RESULT_OK)
                    finish()
                }
            }
        }

        viewModel.openCashDrawerTask.observe(this) { printerList ->
            printerList?.takeIf { it.isNotEmpty() }?.let {
                if (role().bukalaciuang) {
                    Bugsnag.leaveBreadcrumb("opening cash drawer")
                    toast(getString(R.string.opening_cash_drawer))
                    showDialog(true, getString(R.string.open_cash_drawer))
                    viewModel.isOpeningCashDrawer = true

                    lifecycleScope.launch {
                        withTimeoutOrNull(5000L) {
                            openCashDrawer(printerList) { response ->
                                Timber.i("opening cash drawer status: ${response.status} - message: ${response.message}")
                            }
                        }
                        viewModel.isOpeningCashDrawer = false
                    }
                }
            }
        }

        viewModel.navigateToPaymentQr.observe(this) { payment ->
//            paymentQrResultLauncher.launch(Intent(requireContext(), PaymentQrActivity::class.java))
            val intent = Intent(this, PaymentQrActivity::class.java)
            intent.putExtra("data", payment)
            intent.putExtra("total", viewModel.sales.grandTotal)
            intent.putExtra("id", viewModel.sales.noNota)
            startActivityForResult(intent, RC_PAYMENT)
        }

        viewModel.getBankMedia(outlet()?.outletId).observe(this) { bank ->
            bank.data?.let { list ->
                viewModel.bankList.clear()
                viewModel.bankList.addAll(list)
                viewModel.bankList.sortBy { it.name }
                Timber.d("#bank: ${list.map { it.name }}")
            }
        }

        viewModel.taskPaymentDetailDialog.observe(this) { isShow ->
            if (isShow) {
                updatePaymentDetailUi()
            } else {
                dismissPaymentDetailDialog()
            }
        }

        viewModel.taskUseCashDialog.observe(this) {
            showMessage(
                "Kamu belum memilih metode pembayaran, \nGunakan Metode Cash?",
                getString(R.string.confirmation).uppercase(),
                positiveAction = { _, _ ->
                    viewModel.finishPayment(true)
                })
        }
    }

    private fun onSalesSaved(sales: SalesEntity) {
        Firebase.analytics
            .logEvent(
                FirebaseAnalytics.Event.PURCHASE,
                bundleOf(
                    FirebaseAnalytics.Param.CURRENCY to "IDR",
                    FirebaseAnalytics.Param.VALUE to sales.grandTotal.toString()
                )
            )

        val bundle = Bundle()
        bundle.putParcelable("sales", sales)
        bundle.putSerializable("isPayment", true)
        bundle.putSerializable("marge_ids", viewModel.mergeIds)
        val intent = Intent()
        intent.putExtras(bundle)
        setResult(Activity.RESULT_OK, intent)

        if (!viewModel.isOpeningCashDrawer) {
            Timber.i("payment finish...")
            finish()
        } else {
            Timber.i("System is currently trying to open cash drawer. Wait for 10 seconds...")
            //wait until cash drawer opening progress is finished
            lifecycleScope.launch(Dispatchers.Main) {
                withTimeoutOrNull(7000L) {
                    do {
                        Timber.i("wait, opening cashdrawer")
                        delay(500)
                    } while (viewModel.isOpeningCashDrawer)
                }
                showDialog(false)
                finish()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Timber.d("#split onActivityResult, $resultCode, $requestCode")
        if (resultCode == Activity.RESULT_OK && requestCode == RC_SPLITBILL) {
            data?.extras?.getParcelable<SalesEntity?>("sales")?.let { sales ->
                Timber.d("#split, sales: ${sales.grandTotal}")
                if (sales is SalesEntity) {
//                    isAfterSplit = true
                    viewModel.sales = sales
                    binding.layoutHeader.txtGrandTotal.text = viewModel.getGrandTotal().toCurrency()
                    if (sales.orderList?.isEmpty() == true) {
                        showMessage(
                            getString(R.string.all_item_splited_already),
                            onDismissListener = {
                                setResult(Activity.RESULT_OK)
                                finish()
                            })
                    } else
                        Snackbar.make(
                            binding.layoutHeader.btnSplit,
                            getString(R.string.split_bill_success),
                            Snackbar.LENGTH_LONG
                        ).show()
                }
            } ?: run {
                Timber.d("#split, not sales...")
            }
            viewModel.refreshPaymentOption()
        } else if (requestCode == RC_PAYMENT) {
            if (resultCode == Activity.RESULT_OK) {
                viewModel.saveSalesFromPayment()
            } else {
                registerBroadcast()
            }
        }
    }

    private fun registerBroadcast() {
        val br = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.getStringExtra("sync")) {
                    "payment" -> viewModel.saveSalesFromPayment()
                }
            }
        }

//        ContextCompat.registerReceiver(this, br, IntentFilter(Constant.INTENT_SYNC_REQUEST), ContextCompat.RECEIVER_NOT_EXPORTED)
        WeakReference<Context>(this).get()?.apply {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(
                    br,
                    IntentFilter(Constant.INTENT_SYNC_REQUEST),
                    RECEIVER_NOT_EXPORTED
                )
            } else {
                @Suppress("UnspecifiedRegisterReceiverFlag")
                registerReceiver(br, IntentFilter(Constant.INTENT_SYNC_REQUEST))
            }
        }
    }

    private fun initViewFeature() {
        outletFeature().let { feature ->
            binding.layoutHeader.btnSplit.setVisible(feature.splitbill)
        }
    }

    fun getPaymentViewModel(): PaymentViewModel {
        return viewModel
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        onBackPressed()
        return super.onOptionsItemSelected(item)
    }

    override fun onBackPressed() {
        showMessage(
            getString(R.string.warning_bill_not_paid),
            getString(R.string.confirmation),
            { _, _ ->
                super.onBackPressed()
            })
    }

    override fun onDestroy() {
        DataHolder.removeData()
        super.onDestroy()
    }

}