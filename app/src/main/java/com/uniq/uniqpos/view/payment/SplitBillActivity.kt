package com.uniq.uniqpos.view.payment

import android.app.Activity
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.lifecycle.Observer
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.databinding.ActivitySplitBillBinding
import com.uniq.uniqpos.databinding.ListItemMenuCardSplitbillBinding
import com.uniq.uniqpos.databinding.ListItemTaxBinding
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.TaxSales
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import com.uniq.uniqpos.view.transaction.adapter.BillAdapter
import com.uniq.uniqpos.view.transaction.dialog.SaveOrderDialog
import com.uniq.uniqpos.view.transaction.dialog.TaxDetailDialog
import smartdevelop.ir.eram.showcaseviewlib.GuideView
import smartdevelop.ir.eram.showcaseviewlib.config.DismissType
import timber.log.Timber

class SplitBillActivity : BaseActivity<TransactionViewModel, ActivitySplitBillBinding>() {

    private val RC_PAYMENT = 30
    private lateinit var sales: SalesEntity
    private lateinit var taxDetailDialog: TaxDetailDialog
    private var saveOrderDialog: SaveOrderDialog? = null
    private var isTabDevice = false
    private var paymentFlow = ""

    override fun getLayoutRes() = R.layout.activity_split_bill
    override fun getViewModel() = TransactionViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        if (resources.getBoolean(R.bool.landscape_only)) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
            isTabDevice = true
        }
//        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE

        paymentFlow = intent.getStringExtra(PaymentActivity.INTENT_PAY_FLOW) ?: ""

        Timber.i(">>> SPLIT BILL <<<")
        showCase()
    }

    private fun showCase() {
        val employee = employee()
        val keySplitBillAddItem = "split_bill:additem-${employee?.employeeId}"
        if (!getLocalDataBoolean(keySplitBillAddItem)) {
            GuideView.Builder(this)
                .setTitle("Tambah Item")
                .setTitleTextSize(14)
                .setContentText("Tambahkan item yang akan di split dengan memilih dari list item ini")
                .setTargetView(binding.recviewMenu)
                .setDismissType(DismissType.anywhere)
                .build()
                .show()
            putData(keySplitBillAddItem, true)
        }
    }

    override fun initView() {
        super.initView()
        sales = intent.getParcelableExtra("sales")!!
        viewModel.salesEdit = sales.copy()

        sales.taxes?.forEach { tax -> viewModel.taxesEnable.put(tax.id ?: 0, true) }

        saveOrderDialog = object : SaveOrderDialog(viewModel, this@SplitBillActivity) {
            override fun getFragmentManager() = supportFragmentManager
            override fun scanMember(intent: Intent, requestCode: Int) {
                startActivityForResult(intent, requestCode)
            }

            override fun resetTransactionView() {
                finish()
            }

            override fun payOrder(sales: SalesEntity) {
                dismiss()
                navigateToPayment(getSalesEntity(sales.customer ?: "-", sales.customersQty))
            }

            override fun onCreate(savedInstanceState: Bundle?) {
                super.onCreate(savedInstanceState)
                initButton(false)
                initField(predictionField = false, pinField = false, tableField = false)
            }

            override fun startActivityWithResult(intent: Intent, requestCode: Int) {
                startActivityForResult(intent, requestCode)
            }
        }

        val billAdapter = object : BillAdapter(viewModel, this) {
            override fun showVoidDialog(order: Order, employee: Employee) {}
            override fun showMenuDetail(position: Int) {}
            override fun refreshGrandTotal() {
                updateGrandTotal()
            }
        }

        billAdapter.setOnQtyChangeListener { order, _, isAdd ->
            if (isAdd) {
                Timber.i("[split] add qty of ${order.product?.name}")
                sales.orderList?.firstOrNull { it.tmpId == order.tmpId }?.let { order ->
                    if (order.qty > 1) {
                        order.qty -= 1
                    } else {
                        sales.orderList?.remove(order)
                    }
                    binding.recviewMenu.adapter?.notifyDataSetChanged()
                    order.qty > 0
                } ?: kotlin.run { false }
            } else {
                Timber.i("[split] min qty of ${order.product?.name}")
                sales.orderList?.firstOrNull { it.tmpId == order.tmpId }?.let { order ->
                    order.qty += 1
                } ?: kotlin.run { sales.orderList?.add(order.copy(qty = 1)) }
                binding.recviewMenu.adapter?.notifyDataSetChanged()

                var orderJson = ""
                viewModel.orders.forEach { orderJson += "${it.product?.name} (${it.qty}),  " }
                Timber.i("[split] on bill : \n $orderJson")

                true
            }
        }

        binding.recviewBill.adapter = billAdapter
        binding.recviewMenu.adapter =
            object : GlobalAdapter<ListItemMenuCardSplitbillBinding>(
                R.layout.list_item_menu_card_splitbill,
                sales.orderList
            ) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemMenuCardSplitbillBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
                    holder.itemView.setOnClickListener { addItemToSplit(holder.adapterPosition) }
                }
            }

        binding.layoutTax.recviewTax.adapter =
            object :
                GlobalAdapter<ListItemTaxBinding>(R.layout.list_item_tax, viewModel.taxSaleList) {}
        binding.layoutPayment.setOnClickListener {
            if (viewModel.orders.isNotEmpty()) {
                viewModel.isValidSplitBill(sales.orderList)
                    ?.let { e -> showMessage(e.message, "Gagal") }
                    ?: kotlin.run { saveOrderDialog?.show() }
            } else {
                toast("please select item to split!")
            }
        }

        taxDetailDialog = object : TaxDetailDialog(this, viewModel) {
            override fun startActivityWithResult(intent: Intent, requestCode: Int) {
                showMessage("Fitur ini sedang dalam tahap pengembangan!")
            }

            override fun onTaxSaved() {
                updateGrandTotal()
                binding.layoutTax.recviewTax.adapter?.notifyDataSetChanged()
            }
        }

        binding.layoutTax.txtDetailTax.setOnClickListener {
            taxDetailDialog.show()
        }

        binding.layoutTax.groupVoucher.visibility = View.GONE
        binding.layoutTax.txtAddFree.visibility = View.GONE
        binding.layoutTax.imgAddFree.visibility = View.GONE

        Timber.i(">> split this bill : ${Gson().toJson(sales)}")
    }

    private fun addItemToSplit(position: Int) {
        if (position < 0) return
        var isNewItem = false
        var log = ""
        viewModel.orders.firstOrNull { it.tmpId == sales.orderList?.get(position)?.tmpId }
            ?.let { order ->
                order.qty += 1
                viewModel.updatePrice(order) //update subtotal
                log = "existing item in bill"
            } ?: kotlin.run {
            val newOrder = sales.orderList!![position].copy(qty = 1, isHold = false)
            viewModel.updatePrice(newOrder)
            viewModel.orders.add(newOrder)
            isNewItem = true
            log = "new bill"
        }

        log = "[split] adding ${sales.orderList?.get(position)?.product?.name} to $log"
        var orderJson = ""
        viewModel.orders.forEach { orderJson += "${it.product?.name} (${it.qty} -> ${it.subTotal}),  " }
        Timber.i("$log \n[split] on bill : \n $orderJson")

        sales.orderList!![position].qty -= 1
        val order = sales.orderList!![position]

        if (isNewItem) binding.recviewBill.adapter?.notifyItemInserted(viewModel.orders.size)
        else binding.recviewBill.adapter?.notifyDataSetChanged()

        binding.recviewBill.post {
            binding.recviewMenu.adapter?.notifyDataSetChanged()
            viewModel.updateTax(order)
            binding.layoutTax.recviewTax.adapter?.notifyDataSetChanged()
            updateGrandTotal()
            if (isNewItem) binding.recviewBill.smoothScrollToPosition(viewModel.orders.size)
        }

        if (sales.orderList!![position].qty <= 0) {
            sales.orderList!!.remove(order)
        }
    }

    override fun observeData() {
        viewModel.getGratuity()
            .observe(this, Observer { items ->
                viewModel.gratuities.clear()
                items?.data?.let { viewModel.gratuities.addAll(it) }
//                    viewModel.initDefaultTax()
                binding.layoutTax.recviewTax.adapter?.notifyDataSetChanged()
            })

        viewModel.getTax(outlet()?.outletId ?: 0)
            .observe(this, Observer { items ->
                viewModel.taxes.clear()
                items?.data?.let { viewModel.taxes.addAll(it) }
            })
    }

    override fun observeTask() {
        viewModel.taskRefreshTax.observe(this, Observer {
            binding.layoutTax.recviewTax.adapter?.notifyDataSetChanged()
        })

        viewModel.taskMemberFound.observe(this, Observer {
            it?.takeIf { it.status }?.data?.let { member ->
                saveOrderDialog?.setMember(member)
            } ?: run {
                showMessage(it?.message ?: "Not Found!")
            }
        })
    }

    private fun navigateToPayment(salesEntity: SalesEntity) {
        val useNewUi = Firebase.remoteConfig.getBoolean("ui_payment")
        var intent = if(useNewUi) Intent(this, PaymentV2Activity::class.java) else Intent(this, PaymentActivity::class.java)
//        var intent = Intent(this, PaymentActivity::class.java)

        intent.putExtra("sales", salesEntity)
        intent.putExtra("sales_original", sales)
        intent.putExtra(PaymentActivity.INTENT_PAY_FLOW, paymentFlow)
        startActivityForResult(intent, RC_PAYMENT)
    }

    private fun getSalesEntity(customer: String, pax: Int): SalesEntity {
        val newSales = sales.copy(
            orderList = viewModel.orders,
            taxes = viewModel.taxSaleList.filter { it.id != null && it.total > 0 } as java.util.ArrayList<TaxSales>,
            discount = viewModel.discount,
            grandTotal = binding.txtGrandtotal.text.toString().fromCurrency(),
            customer = customer,
            customersQty = pax,
            memberDetail = viewModel.memberDetail,
            memberId = viewModel.memberId)
        val newSalesJson = Gson().toJson(newSales)
//        Timber.i(">> order after split : $newSalesJson")
        recalculateOriginalSales()
        return Gson().fromJson(newSalesJson, SalesEntity::class.java)
    }

    private fun recalculateOriginalSales() {
//        val tmpOrders = Gson().toJson(viewModel.orders)
        //to recalculate, fill data in viewmodel with original sales data, then do calculation
        sales.orderList?.forEach { order -> viewModel.updatePrice(order) }
        viewModel.initSalesData(sales)
        viewModel.reCalculateTax()
//        sales.grandTotal = viewModel.calculateGrandTotal()
//        sales.taxes?.clear()
//        sales.taxes?.addAll(viewModel.taxSaleList.filter { it.id != null && it.total > 0 })
        sales = viewModel.getSalesEntity()
    }

    private fun updateGrandTotal() {
        binding.txtGrandtotal.text = viewModel.calculateGrandTotal().toCurrency()
    }

    override fun onResume() {
        super.onResume()
        Timber.i("[split] onResume... orders : \n${Gson().toJson(viewModel.orders)}")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        saveOrderDialog?.onActivityResult(requestCode, resultCode, data)
        if (requestCode == RC_PAYMENT) {
            if (resultCode == Activity.RESULT_OK) {
                val bundle = Bundle()
                bundle.putParcelable("sales", sales)
                val intent = Intent()
                intent.putExtras(bundle)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
        /* if (resultCode == Activity.RESULT_OK && requestCode == RC_PAYMENT) {
             val bundle = Bundle()
             bundle.putParcelable("sales", sales)
             val intent = Intent()
             intent.putExtras(bundle)
             setResult(Activity.RESULT_OK, intent)
             finish()
         }*/
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
