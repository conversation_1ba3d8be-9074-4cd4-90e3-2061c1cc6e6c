package com.uniq.uniqpos.view.ordersummary

import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.ItemTouchHelper
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.SalesTagEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.clearJson
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Promotion
import com.uniq.uniqpos.databinding.ActivityOrderSummaryBinding
import com.uniq.uniqpos.databinding.ListItemTaxBinding
import com.uniq.uniqpos.model.Discount
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.TaxSales
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.util.printer.PrinterSocket
import com.uniq.uniqpos.util.view.SwipeController
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import com.uniq.uniqpos.view.transaction.adapter.BillAdapter
import com.uniq.uniqpos.view.transaction.dialog.*
import kotlinx.coroutines.runBlocking
import timber.log.Timber

class OrderSummaryActivity : BaseActivity<TransactionViewModel, ActivityOrderSummaryBinding>() {

    private var saveOrderDialog: SaveOrderDialog? = null
    private var taxDetailDialog: TaxDetailDialog? = null
    private lateinit var applyVoucherDialog: ApplyVoucherDialogFragment
    private lateinit var voucherDetailDialog: VoucherDetailDialogFragment
    private lateinit var freeItemListDialog: FreeItemListDialogFragment
    private var outletId = 0
    private lateinit var menuDetailDialog: MenuDetailDialog

    override fun getLayoutRes() = R.layout.activity_order_summary
    override fun getViewModel() = TransactionViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun initView() {
        super.initView()

        viewModel.loadNotesHistory()
        viewModel.loadDiscAndVoucherInfoHistory()

        outletId = outlet()?.outletId.safe()
        initDataFromIntent() //get everything from intent

        val billAdapter = object : BillAdapter(viewModel, this) {
            override fun refreshGrandTotal() {
                <EMAIL>()
            }

            override fun showMenuDetail(position: Int) {
                if (viewModel.orders.size >= position) {
                    showDetailMenuDialog(position)
                }
            }

            override fun showVoidDialog(order: Order, employee: Employee) {
                <EMAIL>(order, employee)
            }
        }

        binding.recviewBill.adapter = billAdapter

        //swipe bill to remove
        val itemTouchHelperBilling =
            ItemTouchHelper(SwipeController(object : SwipeController.SwipeListener {
                override fun enableSwipe(position: Int): Boolean {
                    return !viewModel.orders[position].isItemVoid && !viewModel.orders[position].isHold
                }

                override fun onSwiped(position: Int) {
                    val orderSwiped = viewModel.orders[position]
                    showMessage(
                        getString(R.string.are_you_sure_delete, orderSwiped.product?.name),
                        getString(R.string.delete_item),
                        DialogInterface.OnClickListener { _, _ ->
                            viewModel.removeBill(orderSwiped)
                            binding.recviewBill.adapter?.notifyDataSetChanged()
                            binding.recviewBill.post { binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged() }
                            binding.txtGrandTotal.text =
                                viewModel.calculateGrandTotal().toCurrency()
                        })
                    binding.recviewBill.adapter?.notifyItemChanged(position)
                }
            }))
        itemTouchHelperBilling.attachToRecyclerView(binding.recviewBill)

//        binding.recviewBill.adapter = object : GlobalAdapter(R.layout.list_item_bill, viewModel.orders){}
        binding.layoutTaxDisc.recviewTax.adapter = object :
            GlobalAdapter<ListItemTaxBinding>(R.layout.list_item_tax, viewModel.taxSaleList) {}

        saveOrderDialog = object : SaveOrderDialog(viewModel, this@OrderSummaryActivity) {
            override fun getFragmentManager() = supportFragmentManager
            override fun scanMember(intent: Intent, requestCode: Int) {
                startActivityForResult(intent, requestCode)
            }

            override fun resetTransactionView() {
                setResult(Activity.RESULT_OK)
                finish()
            }

            //move implementation to live data
            override fun onOrderSaved(sales: SalesEntity) {

            }

            override fun startActivityWithResult(intent: Intent, requestCode: Int) {
                startActivityForResult(intent, requestCode)
            }
        }

        binding.btnPay.setOnClickListener {
            if (viewModel.orders.isEmpty()) {
                toast("you have no item on the bill", level = Level.WARNING)
            } else {
                saveOrderDialog?.show()
            }
        }
        taxDetailDialog = object : TaxDetailDialog(this, viewModel) {
            override fun startActivityWithResult(intent: Intent, requestCode: Int) {
                startActivityForResult(intent, requestCode)
            }

            override fun onTaxSaved() {
                binding.txtGrandTotal.text = viewModel.calculateGrandTotal().toCurrency()
                binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
            }
        }

        binding.layoutTaxDisc.txtDetailTax.setOnClickListener { taxDetailDialog?.show() }
        binding.layoutTaxDisc.imgDetail.setOnClickListener { taxDetailDialog?.show() }

        binding.txtAddMore.setOnClickListener { onBackPressed() }

        applyVoucherDialog = ApplyVoucherDialogFragment()
        voucherDetailDialog = VoucherDetailDialogFragment()
        freeItemListDialog = FreeItemListDialogFragment()

        applyVoucherDialog.setViewModel(viewModel)
        voucherDetailDialog.setViewModel(viewModel)
        freeItemListDialog.setViewModel(viewModel)

        binding.layoutTaxDisc.txtVoucher.setOnClickListener {
            if (viewModel.promoApplied.isEmpty()) {
                applyVoucherDialog.show(supportFragmentManager, "voucher")
            } else {
                voucherDetailDialog.show(supportFragmentManager, "voucher_detail")
            }
        }

        binding.layoutTaxDisc.txtAddFree.setOnClickListener {
            freeItemListDialog.show(
                supportFragmentManager,
                "free_item"
            )
        }

        if (!viewModel.isSalesEdit) {
            getJson(SharedPref.TEMP_VOUCHER, Promotion::class.java)?.let { promo ->
                viewModel.promoApplied.add(promo)
                viewModel.taskRefreshPromo.call()
                promo.memberDetail?.let { viewModel.setMember(it) }
            }
        }

        Timber.d("init view order summary...")
        menuDetailDialog = object : MenuDetailDialog(this, viewModel) {
            override fun onMenuDetailSaved(position: Int) {
                binding.recviewBill.adapter?.notifyDataSetChanged()
                binding.recviewBill.post {
                    binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
                    binding.txtGrandTotal.text = viewModel.calculateGrandTotal().toCurrency()
                }
            }
        }

        refreshGrandTotal()
    }

    private fun initDataFromIntent() {
        viewModel.orders.clear()
        viewModel.taxSaleList.clear()
        viewModel.gratuities.clear()
        viewModel.taxes.clear()
        viewModel.loadCustomerNames()

        intent.getParcelableArrayListExtra<Order>("order")
            ?.let { orders -> viewModel.orders.addAll(orders) }
        intent.getParcelableArrayListExtra<TaxSales>("tax_sales")?.let { tax ->
            viewModel.taxSaleList.addAll(tax)
            tax.forEach { viewModel.taxesEnable.put(it.id ?: 0, true) }
        }

        intent.getParcelableExtra<SalesEntity?>("sales")?.let { sales ->
            viewModel.salesEdit = sales
            viewModel.memberDetail = sales.memberDetail
            viewModel.memberId = sales.memberId

            sales.promotions?.let {
                viewModel.promoApplied.addAll(it)
                viewModel.taskRefreshPromo.call()
            }
        }
        (intent.getSerializableExtra("merge_ids") as? ArrayList<String>)?.let {
            viewModel.mergeIds.addAll(
                it
            )
        }
        intent.getParcelableExtra<Discount?>("discount")?.let { viewModel.discount = it }
        viewModel.taxSaleList.forEach {
            viewModel.taxesEnable.put(it.id ?: 0, true)
        }

        viewModel.orders.forEach {
            if (it.isHold) {
                viewModel.isSalesEdit = true
                return@forEach
            }
        }
        Timber.i("SALES >> ${Gson().toJson(viewModel.orders)}")
    }

    override fun observeTask() {
        viewModel.printTask.observe(this, Observer {
            it?.let { result -> print(result.first, it.second) }
        })

        viewModel.pDialogTask.observe(this, Observer { message ->
            showDialog(!isProgressDialogShowing(), message ?: "Loading...")
        })

        viewModel.taskMemberFound.observe(this, Observer {
            it?.takeIf { it.status }?.data?.let { member ->
                saveOrderDialog?.setMember(member)
            } ?: run {
                showMessage(it?.message ?: "Not Found!")
            }
        })

        viewModel.taskShowMessage.observe(this, Observer { msg ->
            showDialog(false)
            if (msg is Exception) {
                showMessage(msg.readableError(this))
            } else if (msg is String) {
                showMessage(msg)
            }
        })

        viewModel.taskVoidVoucher.observe(this, Observer { task ->
            toast("Promo ${task?.name} berhasil di void")
        })

        viewModel.taskRefreshPromo.observe(this, Observer {
            viewModel.promoApplied.firstOrNull()?.let { promo ->
                binding.layoutTaxDisc.txtVoucher.text = promo.displayVoucher
                if (!viewModel.isSalesEdit) putJson(SharedPref.TEMP_VOUCHER, promo)
            } ?: run {
                if (!viewModel.isSalesEdit) clearJson(SharedPref.TEMP_VOUCHER)
                binding.layoutTaxDisc.txtVoucher.text = getString(R.string.have_voucher_code)
            }
            binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
            refreshGrandTotal()
        })

        viewModel.taskRefreshBill.observe(this) {
            binding.recviewBill.adapter?.notifyDataSetChanged()
        }

        viewModel.taskRefreshTax.observe(this) {
            binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
        }

        setupToastMessage(this, viewModel.toastMessage)

        viewModel.taskShowVoucherDetail.observe(this) {
            voucherDetailDialog.show(supportFragmentManager, "voucher_detail")
        }

        viewModel.refreshOrder.observe(this) {
            binding.recviewBill.adapter?.notifyDataSetChanged()
            binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
            refreshGrandTotal()
        }

        viewModel.getProducts(outletId)
            .observe(this, Observer {
                viewModel.products.clear()
                it?.data?.let {
//                        it.distinctBy { it.productId }?.let { filter ->
//                            viewModel.products.addAll(filter)
//                        }
                    viewModel.products.addAll(it)
                }
                viewModel.filterCategory()
                viewModel.refreshProduct.call()
            })

        viewModel.getGratuity()
            .observe(this, Observer { items ->
                viewModel.gratuities.clear()
                items?.data?.let { viewModel.gratuities.addAll(it) }
                viewModel.initDefaultTax()
            })

        viewModel.getTax(outletId)
            .observe(this, Observer { items ->
                viewModel.taxes.clear()
                items?.data?.forEach { viewModel.taxes.add(it) }
                viewModel.reCalculateTax()
                binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
                refreshGrandTotal()
            })

        viewModel.loadMultiplePrice(outletId)
            .observe(this, Observer { items ->
                viewModel.multiplePriceList.clear()
                items?.data?.let { viewModel.multiplePriceList.addAll(it) }
            })

        viewModel.getPromotionLive(outletId)
            .observe(this, Observer { items ->
                items?.data?.let { data ->
                    viewModel.updatePromotionList(data)
                }
            })

        viewModel.taskCartSaved.observe(this, Observer { sales ->
            saveOrderDialog?.dismiss()
            val intent = Intent()
            intent.putExtra("sales", sales)
            setResult(Activity.RESULT_OK, intent)
            finish()
        })

        viewModel.getSalesTagLive().observe(this){ salesTagList ->
            viewModel.salesTagList.clear()
            viewModel.salesTagList.addAll(salesTagList)

            //adding add button
            if(salesTagList.isNotEmpty())
                viewModel.salesTagList.add(SalesTagEntity(0,0,"", 0,0,"+Add"))
        }
    }

    private fun print(printData: List<PendingPrintEntity>, isCloseAfterPrint: Boolean) {
        runBlocking {
            managePrintWifi(printData
            ) { isConnected, message ->
                if (!isConnected) {
                    viewModel.savePendingPrint(printData)
                }
                if (isCloseAfterPrint) resetTransaction()
            }

            if (printData.isEmpty() && isCloseAfterPrint) resetTransaction()
        }
    }

    private fun showDetailMenuDialog(position: Int) {
        menuDetailDialog.show(position)
    }

    private fun refreshGrandTotal() {
        binding.txtGrandTotal.text = viewModel.calculateGrandTotal().toCurrency()
    }

    private fun showVoidDialog(order: Order, employee: Employee) {
        VoidDialog(this, order, employee, viewModel)
            .setOnVoidSaved {
                if (viewModel.isParentFromPromo(order)) {
                    viewModel.removeFreeItemByParentId(order.tmpId)
                }
                binding.recviewBill.adapter?.notifyItemInserted(viewModel.orders.size - 1)
                binding.recviewBill.smoothScrollToPosition(viewModel.orders.size - 1)

                binding.recviewBill.post {
                    binding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
                    viewModel.runGrandTotalWatcher()

                    val sales = getSalesEntity()
                    val tmpSales = TmpSalesEntity(sales!!.noNota, Gson().toJson(sales), outletId)
                    if (viewModel.isSalesEdit) viewModel.updateTmpSale(tmpSales)
                }
            }.show()
    }

    private fun resetTransaction() {
        setResult(Activity.RESULT_OK)
        finish()
    }

    private fun getSalesEntity(): SalesEntity {
        return viewModel.getSalesEntity()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        onBackPressed()
        return super.onOptionsItemSelected(item)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        saveOrderDialog?.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == SaveOrderDialog.RC_PAYMENT) {
                val intent = Intent()
                data?.extras?.let { intent.putExtras(it) }
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }

    override fun onBackPressed() {
        Timber.i("<< BACK")
        val intent = Intent()
        intent.putExtra("from", OrderSummaryActivity::class.simpleName)
        intent.putExtra("sales", viewModel.getSalesEntity())
        setResult(Activity.RESULT_OK, intent)
        super.onBackPressed()
    }
}
