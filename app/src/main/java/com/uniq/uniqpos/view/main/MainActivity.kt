package com.uniq.uniqpos.view.main

import android.app.Activity
import android.app.DownloadManager
import android.app.ProgressDialog
import android.content.BroadcastReceiver
import android.content.ContentResolver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.PersistableBundle
import android.os.StatFs
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.view.GravityCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.bugsnag.android.Bugsnag
import com.google.android.material.navigation.NavigationView
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.gu.toolargetool.TooLargeTool
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.binding.setting.SettingFragment
import com.uniq.uniqpos.data.local.dao.SettingDao
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.entity.ReservationEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.clearJson
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.data.remote.service.SalesService
import com.uniq.uniqpos.data.remote.service.SystemService
import com.uniq.uniqpos.databinding.ActivityMainBinding
import com.uniq.uniqpos.databinding.NavHeaderMainBinding
import com.uniq.uniqpos.model.Admin
import com.uniq.uniqpos.model.SubscriptionStatus
import com.uniq.uniqpos.sync.UniqSyncAdapter
import com.uniq.uniqpos.sync.scheduleJob
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.NetworkUtil
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.appendnl
import com.uniq.uniqpos.util.awaitAsync
import com.uniq.uniqpos.util.bundleOf
import com.uniq.uniqpos.util.dateFormat
import com.uniq.uniqpos.util.dateTimeFormat
import com.uniq.uniqpos.util.diffMinute
import com.uniq.uniqpos.util.employee
import com.uniq.uniqpos.util.getDeviceName
import com.uniq.uniqpos.util.intent.CameraOrGalleryIntent
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.outletFeature
import com.uniq.uniqpos.util.printer.PrinterManager
import com.uniq.uniqpos.util.readableError
import com.uniq.uniqpos.util.role
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toRequestBody
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.billing.BillingActivity
import com.uniq.uniqpos.view.cashdrawer.InputCashDrawerFragment
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import com.uniq.uniqpos.view.closeshift.CloseShiftFragment
import com.uniq.uniqpos.view.help.HelpFragment
import com.uniq.uniqpos.view.login.LoginAdminActivity
import com.uniq.uniqpos.view.ordersales.OrderSalesActivity
import com.uniq.uniqpos.view.pendingprint.PendingPrintActivity
import com.uniq.uniqpos.view.piutang.PiutangActivity
import com.uniq.uniqpos.view.productcatalogue.AddProductMainActivity
import com.uniq.uniqpos.view.purchase.OperationalCostFragment
import com.uniq.uniqpos.view.reservation.ReservationActivity
import com.uniq.uniqpos.view.runoutstock.RunOutOfStockActivity
import com.uniq.uniqpos.view.transaction.TransactionMainFragment
import com.uniq.uniqpos.view.transactionhistory.TransactionHistoryFragment
import dagger.android.AndroidInjection
import dagger.android.DispatchingAndroidInjector
import dagger.android.support.HasSupportFragmentInjector
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.RequestBody
import timber.log.Timber
import java.io.File
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone
import javax.inject.Inject

class MainActivity : AppCompatActivity(), NavigationView.OnNavigationItemSelectedListener,
    HasSupportFragmentInjector {

    @Inject
    lateinit var fragmentDispatchingInjector: DispatchingAndroidInjector<Fragment>

    @Inject
    lateinit var printerManager: PrinterManager

    @Inject
    lateinit var settingDao: SettingDao

    @Inject
    lateinit var salesService: SalesService

    @Inject
    lateinit var systemService: SystemService

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    private val RC_RESERVATION = 1
    private lateinit var viewModel: MainViewModel
    private var selectedMenu = R.id.nav_transaction
    private var broadcastReceiver: BroadcastReceiver? = null
    private var downloadUpdateId: Long? = null
    private var updateApkPath: String? = null
    private lateinit var navHeader: NavHeaderMainBinding

    var isTabLayout = false
        private set

    private lateinit var binding: ActivityMainBinding
    lateinit var pDialog: ProgressDialog

    //apps ui's
    private val transactionFragment = TransactionMainFragment()

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onCreate(savedInstanceState: Bundle?) {
        AndroidInjection.inject(this)
        super.onCreate(savedInstanceState)
//        binding = ActivityMainBinding.inflate(layoutInflater)
//        setContentView(binding.root)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_main)
        viewModel = ViewModelProvider(this, viewModelFactory).get(MainViewModel::class.java)

        savedInstanceState?.let { state ->
            val report =  TooLargeTool.bundleBreakdown(state)
            Timber.d("bundleBreakdown: $report")
        }

        pDialog = ProgressDialog(this)
        pDialog.setCancelable(false)
        pDialog.setMessage("Loading...")

        Timber.i("[STARTING] onCreate is start to run...")
        Timber.d("[LIFE] #onCreate")
        Bugsnag.leaveBreadcrumb("starting app")

        window.setBackgroundDrawable(null)
        setSupportActionBar(binding.incAppBar.incToolbar.toolbar)
        if (resources.getBoolean(R.bool.landscape_only)) {
            Bugsnag.leaveBreadcrumb("request landscape orientation")
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
            isTabLayout = true

            //if current orientation is potrait, activity need to re-create, so skip all code below
            //as all of which will run again (onCreate will call again)
            //but, if current fragment isn't transaction, just skip it, we need any value within bundle
            if (resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT && selectedMenu != R.id.nav_transaction) {
                logInfo()
                return
            }
        }

        var version = BuildConfig.VERSION_NAME
        if (version.contains("-")) {
            version =
                BuildConfig.VERSION_NAME.substring(0, BuildConfig.VERSION_NAME.lastIndexOf("-"))
        }
        if (BuildConfig.FLAVOR === "production") {
            binding.txtNav.text = "UNIQ $version"
        } else {
            binding.txtNav.text = "UNIQ POS $version"
        }

        val toggle = object: ActionBarDrawerToggle(
            this,
            binding.drawerLayout,
            binding.incAppBar.incToolbar.toolbar,
            R.string.navigation_drawer_open,
            R.string.navigation_drawer_close
        ){
            override fun onDrawerOpened(drawerView: View) {
                super.onDrawerOpened(drawerView)
                showSubscriptionStatus()
            }
        }
        binding.drawerLayout.addDrawerListener(toggle)
        toggle.syncState()

        val admin = getJson(SharedPref.ADMIN_DATA, Admin::class.java)
        val outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        val employee = getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java)
        navHeader = NavHeaderMainBinding.bind(binding.navView.getHeaderView(0))
        navHeader.outlet = outlet
        navHeader.employee = employee
        navHeader.txtActionSubscribe.setVisible(false)

        showSubscriptionStatus()

        if (outlet == null) {
            toast("Some required data is empty. Please try re-login", level = Level.WARNING)
            Bugsnag.notify(
                java.lang.Exception(
                    "outlet is NULL. admin: (${admin?.adminId}):${admin?.email}:${
                        getLocalDataString(
                            SharedPref.DEVICE_ID,
                            ""
                        )
                    }"
                )
            )
            putData(SharedPref.LOGIN_ADMIN_STATUS, false)
            putData(SharedPref.LOGIN_EMPLOYEE_STATUS, false)
            clearJson(SharedPref.ADMIN_DATA)
            val intent = Intent(this, LoginAdminActivity::class.java)
            startActivity(intent)
            finish()
        }

        val status = NetworkUtil.getConnectivityStatusString(applicationContext)
        navHeader.online = status != NetworkUtil.NETWORK_STATUS_NOT_CONNECTED
//        navHeader.shift
        if (status == NetworkUtil.NETWORK_STATUS_NOT_CONNECTED) {
            Snackbar.make(
                binding.drawerLayout,
                getString(R.string.offline_message),
                Snackbar.LENGTH_LONG
            )
                .setAction("OK", null).show()
        }

        binding.navView.setNavigationItemSelectedListener(this)

        try {
            broadcastReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    when (intent?.getStringExtra("action")) {
                        Constant.ACTION_FINISH_ALL_ACTIVITIES -> finish()
                        Constant.STATE_FINISH_SYNCED -> {
                            getTransactionMain()?.refreshViewAfterSynced()
                            initNavigationMenuVisibility()
                        }
                        Constant.ACTION_CLOSE_AND_OPEN_SETTING -> {
                            val setting = intent.getStringExtra("setting")
                            showMessage("Jam/Tanggal pada device anda sepertinya belum di setting dengan benar. \n" +
                                    "Silahkah buka Pengaturan dan ganti tanggal terlebih dahulu!",
                                "JAM/TANGAL BERBEDA",
                                onDismissListener = DialogInterface.OnDismissListener {
                                    startActivity(Intent(setting))
                                    finish()
                                })
                        }
                        Constant.STATE_NETWORK_STATUS -> {
                            val connectionStatus = intent.getBooleanExtra("network_status", false)
                            navHeader.online = connectionStatus
                            if (!connectionStatus)
                                Snackbar.make(
                                    binding.drawerLayout,
                                    getString(R.string.offline_message),
                                    Snackbar.LENGTH_LONG
                                ).setAction("OK", null).show()
                        }
                    }

                    //handle download complete
                    intent?.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                        ?.takeIf { it == downloadUpdateId }?.let {
                            toast("download completed")
                            showMessage(
                                "latest apk downloaded, install now?",
                                "DOWNLOAD UPDATE COMPLETED",
                                { _, _ ->
                                    installLatestApk()
                                })
                        }
                }
            }
        } catch (e: Exception) {
            toast("set broadcastReceiver error $e")
        }

        try {
            val broadcastIntentFilter = IntentFilter()
            broadcastIntentFilter.addAction(Intent.ACTION_VIEW)
            broadcastIntentFilter.addAction(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                toast("register receiver on > tiramisu")
                registerReceiver(broadcastReceiver, broadcastIntentFilter, RECEIVER_NOT_EXPORTED)
            }else{
                toast("registering receiver...")
                @Suppress("UnspecifiedRegisterReceiverFlag")
                registerReceiver(broadcastReceiver, broadcastIntentFilter)
            }
        } catch (e: Exception) {
            Timber.i("register receiver error $e")
            toast("register receiver error $e")
        }

//        runBlocking {
//            val printers = GlobalScope.async { settingDao.getPrinters() }.await()
//            if (printers.isNotEmpty()) createPrinterConnection(printers[0])
//        }

        startSyncWorker()

        val warningMessage: String? = intent.getStringExtra("warning_message")
        warningMessage?.let { showMessage(it) }

        Timber.i("savedInstanceState null? ${savedInstanceState == null}")
        if (savedInstanceState == null) {
            initNavigationMenuVisibility()
            checkCloseShift()
            checkAutoSync()
            deleteOldLog()
            setBillingStatus()
            checkLowDiskSpace()
            checkUpdate()
            logInfo()
        }

        if (admin?.adminId == null || admin.adminId == 0) {
            showMessage(
                getString(R.string.failed_validate_login),
                "ERROR",
                onDismissListener = {
                    putData(SharedPref.LOGIN_ADMIN_STATUS, false)
                    clearJson(SharedPref.ADMIN_DATA)
                    val intent = Intent(this, LoginAdminActivity::class.java)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(intent)
                    finish()
                })
        }

        //init New label
//        (MenuItemCompat.getActionView(binding.navView.menu.findItem(R.id.nav_cash_management)) as? TextView )?.let { txt ->
//            txt.setTextColor(Color.parseColor("#E28619"))//96999F
//            txt.textSize = 10f
//            txt.text = "New"
//        }

        //savedInstanceState?.apply { selectedMenu = getInt("selected") }
        onNavigationItemSelected(binding.navView.menu.findItem(selectedMenu))

        viewModel.checkLatestVersion()
        observe()

        Timber.i("[STARTING] onCreate is finish...")
        Bugsnag.leaveBreadcrumb("app ready")
    }

    private fun startSyncWorker() {
        try {
            UniqSyncAdapter.initializeSyncAdapter(this)
        } catch (e: Exception) {
            toast("error initialize sync adapter: $e")
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            scheduleJob()
        }
        try {
            scheduleJob()
        } catch (e: Exception) {
            toast("error schedule job: $e")
        }
        try {
            UniqSyncAdapter.syncImmediately(this)
        } catch (e: Exception) {
            toast("error sync immediately: $e")
        }
    }

    private fun watchSynchronization(){
        val lastSyncStart  = sharedPref().getString(SharedPref.LAST_SYNC, "0").safe("0").toLong()
        val startIme = System.currentTimeMillis()
        startSyncWorker()
        pDialog.show()

        lifecycleScope.launch {
            var lastSync = 0L
            do {
                delay(1500)
                val diffMinutes = startIme.diffMinute()
                lastSync =  sharedPref().getString(SharedPref.LAST_SYNC, "0").safe("0").toLong()
                Timber.i("watchSynchronization, at start: $lastSyncStart | now: $lastSync | diff: $diffMinutes")
            }while(lastSync == lastSyncStart && diffMinutes < 3)

            runOnUiThread {
                pDialog.dismiss()
                if(lastSyncStart == lastSync){
                    showMessage("singkronasi tidak dapat berjalan dengan semestinya, pastikan koneksi internet anda stabil!", "Sync Failed")
                    checkLowDiskSpace()
                }
            }
        }
    }

    private fun showSubscriptionStatus() {
        val subscription = getJson(SharedPref.SUBSCRIPTION_STATUS, SubscriptionStatus::class.java)
        subscription?.takeIf { !it.subscriptionMessage.isNullOrBlank() }?.let { _ ->
            navHeader.subscription = subscription
        } ?: run {
            navHeader.subscription = SubscriptionStatus(subscriptionMessage = "...", deviceLogin = ArrayList())
        }

//        if (BuildConfig.FLAVOR == "development" || (subscription?.timeExpiredCount.safe() <= 7 && ((subscription?.subscriptionType == "trial" && !subscription.subscribed) || subscription?.subscriptionType != "trial"))) { }
        navHeader.txtActionSubscribe.setVisible(true)
        navHeader.txtActionSubscribe.setOnClickListener {
//                launchUrl("${BuildConfig.WEB_URL}settings/subscription-add")
            startActivity(Intent(this, BillingActivity::class.java))
        }
        navHeader.txtSubscription.setTextColor(
            ContextCompat.getColor(
                this,
                R.color.red_background
            )
        )
    }

    private fun observe() {
        viewModel.loadCurrentShift()
        viewModel.eventNewRelease.observe(this) { app ->
            updateApkPath =
                getExternalFilesDir(Environment.DIRECTORY_PICTURES).toString() + "/${app.versionName}.apk"

            //if already downloaded, prompt to install
            val isFileDownloaded = File(updateApkPath).isFile
            showMessage(
                "update versi ${app.versionName} tersedia.\n\nRelease Notes: \n${app.releaseNote}",
                "UPDATE AVAILABLE",
                { _, _ ->
                    if (isFileDownloaded) {
                        installLatestApk()
                    } else {
                        app.fileUrl?.let { url ->
                            downloadLatestApk(url, app.versionName)
                        } ?: run {
                            toast("can not download file...")
                        }
                    }
                }, positiveMsg = if (isFileDownloaded) "INSTALL" else "DOWNLOAD"
            )
        }

        navHeader.shiftName = "-"
        viewModel.shiftNameLive.observe(this) { shiftName ->
            navHeader.shiftName = shiftName
        }
    }

    private fun logInfo() {
        val admin = getJson(SharedPref.ADMIN_DATA, Admin::class.java)
        val outlet = outlet()
        val employee = employee()

        val displayMetrics = resources.displayMetrics
        val dpHeight = displayMetrics.heightPixels / displayMetrics.density
        val dpWidth = displayMetrics.widthPixels / displayMetrics.density
        val config = resources.configuration

        val infoBuffer = StringBuilder()
        infoBuffer.append("-------------------------------------\n");
        infoBuffer.append("Name        : " + getDeviceName() + "\n")
        infoBuffer.append("Model       : " + Build.MODEL + "\n") //The end-user-visible name for the end product.
        infoBuffer.append("Device      : " + Build.DEVICE + "\n") //The name of the industrial design.
        infoBuffer.append("Manufacturer: " + Build.MANUFACTURER + "\n") //The manufacturer of the product/hardware.
        infoBuffer.append("Board       : " + Build.BOARD + "\n") //The name of the underlying board, like "goldfish".
        infoBuffer.append("Brand       : " + Build.BRAND + "\n") //The consumer-visible brand with which the product/hardware will be associated, if any.
        infoBuffer.append("Serial      : " + Build.SERIAL + "\n")
        infoBuffer.append("IMEI        : " + getLocalDataString(SharedPref.DEVICE_ID, "") + "\n")
        infoBuffer.append("-------------------------------------\n")
        infoBuffer.append("Version     : ${BuildConfig.VERSION_NAME}\n")
        infoBuffer.append("Version Code: ${BuildConfig.VERSION_CODE}\n")
        infoBuffer.append("Package     : ${BuildConfig.APPLICATION_ID}\n")
        infoBuffer.append("OS          : ${Build.VERSION.SDK_INT}\n")
        infoBuffer.append("Screen Width: ${config.smallestScreenWidthDp}\n")
        infoBuffer.append("Architecture: ${System.getProperty("os.arch")}\n")
        infoBuffer.append("Heigh       : $dpHeight\n")
        infoBuffer.append("Width       : $dpWidth\n\n")
        Timber.i("### DEVICE INFO \n $infoBuffer")

        val infoTime = StringBuilder()
        infoTime.appendnl(
            "DISPLAY NAME -> ${
                TimeZone.getDefault().getDisplayName(false, TimeZone.SHORT)
            }"
        )
        infoTime.appendnl("ID -> ${TimeZone.getDefault().id}")
        infoTime.appendnl(
            "Offset -> ${
                TimeZone.getDefault().getOffset(System.currentTimeMillis())
            }"
        )
        infoTime.appendnl("timeMillis -> ${System.currentTimeMillis()}")
        Timber.i("### TIME INFO ------> \n$infoTime")

        val loginInfo = StringBuilder()
        loginInfo.appendnl("Admin    : ${admin?.email} (${admin?.adminId})")
        loginInfo.appendnl("Employee : ${employee?.name} (${employee?.employeeId})")
        loginInfo.appendnl("Outlet   : ${outlet?.name} (${outlet?.outletId})")
        Timber.i("#LOGIN INFO  \n$loginInfo")

        Timber.i(
            "[SUBSCRIPTION] ${
                Gson().toJson(
                    getJson(
                        SharedPref.SUBSCRIPTION_STATUS,
                        SubscriptionStatus::class.java
                    )
                )
            }"
        )

        Timber.i("Outlet Feature : ${Gson().toJson(outletFeature())}")

        //analytic purpose
        val analytic = Firebase.analytics
        analytic.logEvent(
            "device_info",
            bundleOf(
                "Architecture" to System.getProperty("os.arch"),
                "Screen Size" to "${config.smallestScreenWidthDp}dp",
                "Device" to "${Build.BRAND} ${Build.MODEL}",
                "Serial" to Build.SERIAL,
                "Detail" to "${Build.BRAND} ${Build.MODEL} - ${Build.SERIAL}"
            )
        )

        analytic.setUserProperty("email", admin?.email)
        analytic.setUserProperty("name", admin?.name)
        analytic.setUserProperty("id", admin?.adminId?.toString())
        analytic.setUserProperty("gmt", TimeZone.getDefault().getDisplayName(false, TimeZone.SHORT))

        Bugsnag.setUser(
            "${admin?.adminId}:${outlet?.outletId}:${
                getLocalDataString(
                    SharedPref.DEVICE_ID,
                    "-"
                )
            }",
            admin?.email,
            "${outlet?.name}:${employee?.name}"
        )

        FirebaseCrashlytics.getInstance().setUserId(
            "${admin?.adminId}:${outlet?.outletId}:${
                getLocalDataString(
                    SharedPref.DEVICE_ID,
                    "-"
                )
            }"
        )
    }

    private fun checkUpdate() {
        WeakReference(applicationContext).get()?.apply {
            val manager = AppUpdateManagerFactory.create(this)
            val appUpdateTask = manager.appUpdateInfo
            appUpdateTask.addOnSuccessListener { appUpdateInfo ->
                if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                    && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)
                ) {
                    manager.startUpdateFlowForResult(
                        appUpdateInfo,
                        AppUpdateType.IMMEDIATE,
                        this@MainActivity,
                        10
                    )

                    val outlet = outlet()
                    Firebase.analytics
                        .logEvent(
                            "app_features",
                            bundleOf(
                                "Feature" to "In App Update",
                                "Outlet" to "IAU:${outlet?.outletId}:${outlet?.name}"
                            )
                        )
                }
            }
        }
    }

    private fun checkLowDiskSpace() {
        val availableSpace = cacheDir.usableSpace * 100 / cacheDir.totalSpace
        if (availableSpace <= 10) { // Alternatively, use cacheDir.freeSpace
            Timber.i("lowspace, under $availableSpace%, useable: ${cacheDir.usableSpace} total available: ${cacheDir.totalSpace}")
            //  showMessage(getString(R.string.low_disk), "WARNING")
            try {
                val path = Environment.getDataDirectory() // or any directory you care about
                val stat: StatFs = StatFs(path.path)
                // API 18+:
                val availableBytes: Long = stat.availableBytes
                Timber.i("Low Disk Space Warning! available : $availableBytes")

                // Decide your “low” threshold, e.g. 50 MB:
                val lowThreshold = 50L * 1024 * 1024
                if (availableBytes < lowThreshold) {
                    // low on storage
                }
            } catch (e: Exception) {
                Timber.i("Low Disk Space Warning! error : $e")
            }

            toast(getString(R.string.low_disk), level = Level.WARNING, duration = Toast.LENGTH_LONG)
            Timber.i(
                "Low Disk Space Warning! available : $availableSpace | last sync : ${
                    getLocalDataString(
                        SharedPref.LAST_SYNC
                    )
                }"
            )
            val outlet = outlet()
            Firebase.analytics
                .logEvent(
                    "warning",
                    bundleOf(
                        "Message" to "Low Disk Space",
                        "Outlet" to "LDS:${outlet?.outletId}:${outlet?.name}",
                        "Available" to "LDS:${outlet?.outletId}:$availableSpace"
                    )
                )
        }
    }

    private fun setBillingStatus() {
//        getJson(SharedPref.ADMIN_DATA, SubscriptionStatus::class.java)?.let { subscriptionStatus ->
//        }
    }

    private fun checkAutoSync() {
        //check if auto sync is enable for all account
        if (!ContentResolver.getMasterSyncAutomatically()) {
            toast(
                "Auto sync is disabled in your setting. System will turn it on automatically",
                Toast.LENGTH_LONG
            )
            ContentResolver.setMasterSyncAutomatically(true)
        }
    }

    private fun checkCloseShift() {
        getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)?.takeIf { it.openShiftId > 0 }
            ?.let { shiftOpen ->
//            val format = "yyyy-MM-dd"
//            val sdf = SimpleDateFormat(format)
//            val timeOpen = sdf.parse(shiftOpen.timeOpen?.dateFormat(format)) -> ini jika tdk memprdulikan jam, hanya tanggal
//            val timeNow = sdf.parse(Date().time.dateFormat(format)) -> ini jika tdk memprdulikan jam, hanya tanggal
                val dateDiff =
                    System.currentTimeMillis() - shiftOpen.timeOpen // timeNow.time - timeOpen.time -> ini jika tdk memprdulikan jam, hanya tanggal
                var diffDays = dateDiff / (1000 * 60 * 60 * 24)
                var diffHour = dateDiff / (1000 * 60 * 60)
                Timber.i("Last Open Shift : ${shiftOpen.timeOpen.dateTimeFormat()} || Dif Day : $diffDays || Dif Hour : $diffHour")
                if (diffDays <= 0 && diffHour > 8 && shiftOpen.timeOpen.dateFormat() != System.currentTimeMillis()
                        .dateFormat()
                ) {
                    diffDays = 1
                }

                if (diffDays > 0) {
                    showSnackbar(
                        getString(R.string.info_not_closing_shift_since, diffDays),
                        Snackbar.LENGTH_INDEFINITE
                    )
                }
            }
    }

    private fun deleteOldLog() {
        if (BuildConfig.FLAVOR !== "development") return

        val parentFolder = File(Environment.getExternalStorageDirectory(), "UNIQ")
        val sdf = SimpleDateFormat("yyyy_MM_dd")
        parentFolder.list()?.forEach { list ->
            try {
                val dateStart = sdf.parse(list)
                val dateDiff = dateStart.time - Date().time
                var diffDays = dateDiff / (1000 * 60 * 60 * 24)
                Timber.i("DIFF DAY => $list AND ${sdf.format(Date())} : $diffDays")
                if (diffDays < 0) diffDays *= -1
                if (diffDays > 7) {
                    val folderLog = File(parentFolder, list)
                    folderLog.list()?.forEach { child ->
                        File(parentFolder, "$list/$child").delete()
                    }
                    folderLog.delete()
                }
            } catch (e: Exception) {
                Timber.i(">>> Try to delete log files error : $e")
            }
        }
    }

    private fun downloadLatestApk(url: String, version: String) {
        if (updateApkPath.isNullOrBlank()) {
            return
        }

        val fileUri = Uri.parse("file://$updateApkPath")
        val request = DownloadManager.Request(Uri.parse(url))
            .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE)
            .setDestinationUri(fileUri)
            .setTitle("Download Update")
            .setDescription("new version $version")
//            .setRequiresCharging(false)
            .setAllowedOverMetered(true)
            .setAllowedOverRoaming(true)

        val downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        downloadUpdateId = downloadManager.enqueue(request)
        toast("download starting.. you will be notified once completed", duration = Toast.LENGTH_LONG)
        Timber.i("download new apk starting with id: $downloadUpdateId")
    }

    private fun installLatestApk() {
        if (updateApkPath.isNullOrBlank()) {
            return
        }

        val fileUri = Uri.fromFile(File(updateApkPath))
        val contentUri =
            FileProvider.getUriForFile(
                this,
                getString(R.string.file_authority),
                File(updateApkPath)
            )
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            intent.putExtra(Intent.EXTRA_NOT_UNKNOWN_SOURCE, true)
            intent.data = contentUri
            startActivity(intent)
        } else {
//            val fileUri = Uri.parse("file://$destination")
            val intent = Intent(Intent.ACTION_VIEW)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            intent.setDataAndType(fileUri, "application/vnd.android.package-archive")
            startActivity(intent)
        }
    }

    private fun initNavigationMenuVisibility() {
//        outletFeature()?.let { feature ->
//            binding.navView.menu.findItem(R.id.nav_transc_history).isVisible = feature.viewtransactionhistory
//            binding.navView.menu.findItem(R.id.nav_close_cashier).isVisible = feature.viewcloseregister
//        }
    }

    private fun createPrinterConnection(printers: PrinterEntity) {
        if (printers.type == Constant.PRINTER_TYPE_BT) {
//            printerManager.printerBluetooth.createConnection(printers.address, {
//                //if(!it) createPrinterConnection(printers)
//            })
        }
    }

    override fun onBackPressed() {
        Timber.i("[onBackPressed]")
        if (binding.drawerLayout.isDrawerOpen(GravityCompat.START)) {
            binding.drawerLayout.closeDrawer(GravityCompat.START)
        } else {
            super.onBackPressed()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        val inflater: MenuInflater = menuInflater
        inflater.inflate(R.menu.main, menu)
        Timber.i("[LIFE] onCreateOptionsMenu")
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_run_out -> startActivity(Intent(this, RunOutOfStockActivity::class.java))
            R.id.action_reservation -> {
                if (BuildConfig.FLAVOR === "production") {
                    Firebase.analytics.logEvent(
                        "feature_lock",
                        bundleOf("feature" to "Reservation")
                    )
                } else
                    startActivityForResult(
                        Intent(this, ReservationActivity::class.java),
                        RC_RESERVATION
                    )
            }
            R.id.action_product -> {
                if (role().productCreate) {
//                    startActivity(Intent(this, AddProductActivity::class.java))
                    startActivity(Intent(this, AddProductMainActivity::class.java))
                } else {
                    showMessage(getString(R.string.no_permission))
                }
            }
            R.id.action_pending_print -> startActivity(
                Intent(
                    this,
                    PendingPrintActivity::class.java
                )
            )
            R.id.action_order_sales -> startActivity(Intent(this, OrderSalesActivity::class.java))
            R.id.action_piutang -> startActivity(Intent(this, PiutangActivity::class.java))
            R.id.action_send_report -> sendReport()
            R.id.action_sync -> watchSynchronization()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun sendReport() {
        if (BuildConfig.FLAVOR === "production") {
            showMessage("This feature is still in development stage")
            return
        }

        showMessage(
            "This will include all logs available",
            "REPORT",
            { _, _ ->
                val folderParent = File(filesDir, "logcat")
                val filePathList = ArrayList<String>()
                folderParent.list()?.forEach { file ->
                    try {
//                    val currentFile = SimpleDateFormat("yyyy_MM_dd HH").format(Date())
                        val logFile = File(folderParent, file)
                        filePathList.add(logFile.path)
                    } catch (e: Exception) {
                        Timber.d("Delete log file error - $e")
                        showMessage(e.readableError(this))
                    }
                }

                if (filePathList.isNotEmpty()) {
                    Timber.i("Uploading.. $filePathList")
                    uploadLog(filePathList, filePathList.size - 1)
                } else {
                    showMessage("No log file found!")
                }

            },
            positiveMsg = "Continue"
        )
    }

    private fun uploadLog(fileList: List<String>, position: Int, result: String = "") {
        if (position < 0) {
            showMessage(result, "RESULT")
            return
        }
        val data = HashMap<String, RequestBody>()
        data["device"] = "${Build.BRAND} ${Build.MODEL}".toRequestBody()
        data["imei"] = getLocalDataString(SharedPref.DEVICE_ID, "").toRequestBody()
        data["outlet_id"] = (outlet()?.outletId?.toString() ?: "").toRequestBody()
        val filePart = Utils.requestBodyFile(fileList[position], Utils.MediaTypes.TEXT_FILE, "file")
        systemService.uploadLog(data, filePart).awaitAsync(
            {
                var status = "Failed"
                if (it.isSuccessful) {
                    status = "Success"
                    try {
                        val folderParent = File(filesDir, "logcat")
                        File(folderParent, File(fileList[position]).name).delete()
                    } catch (e: Exception) {
                    }
                }
                uploadLog(fileList, position - 1, "$result $status \n")
            },
            { err -> uploadLog(fileList, position - 1, "$result ${err.readableError(this)} \n") },
            this
        )
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        val fragment: Fragment? = when (item.itemId) {
            R.id.nav_close_cashier -> CloseShiftFragment()
            R.id.nav_transc_history -> TransactionHistoryFragment()
            R.id.nav_setting -> SettingFragment()
            R.id.nav_change_operator -> changeOperator()
            R.id.nav_cash_management -> OperationalCostFragment()
            R.id.nav_help -> HelpFragment()
            else -> openTransaction()
        }

        Bugsnag.leaveBreadcrumb("navigate to fragment $fragment")
        item.isChecked = true
        binding.drawerLayout.closeDrawer(GravityCompat.START)

        fragment?.let {
            binding.drawerLayout.post {
                Timber.i("post --> it.isDetached ${it.isDetached}, remove: ${it.isRemoving}, added: ${it.isAdded}")
            }
            Timber.i("it.isDetached ${it.isDetached}, remove: ${it.isRemoving}, added: ${it.isAdded} | fragment: $fragment")
            Timber.d("container: ${findViewById<View?>(R.id.container)} ${R.id.container}")
//            findViewById<FrameLayout?>(R.id.container)?.let {
//                supportFragmentManager.beginTransaction()
//                    .replace(R.id.container, fragment, fragment.javaClass.simpleName)
//                    .commit()
//            }

            supportFragmentManager.commit {
//                Timer().schedule(2000) {
//                    replace(R.id.container, fragment)
//                }
                replace(R.id.container, fragment)
            }
        }
        selectedMenu = item.itemId
        return true
    }

    private fun openTransaction(): Fragment {
        val shift = getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)

        Timber.i("OpenShiftId ${shift?.openShiftId}")
        return if (shift != null && shift.openShiftId != 0L) transactionFragment else InputCashDrawerFragment()
    }

    fun getTransactionMain(): TransactionMainFragment? {
        val fragment = supportFragmentManager.findFragmentById(R.id.container)
        return if (selectedMenu == R.id.nav_transaction && fragment is TransactionMainFragment) fragment else null
    }

    fun getCloseShiftFragment(): CloseShiftFragment? {
        val fragment = supportFragmentManager.findFragmentById(R.id.container)
        return if (selectedMenu == R.id.nav_close_cashier && fragment is CloseShiftFragment) fragment else null
    }

    fun getTransactionHistoryFragment(): TransactionHistoryFragment? {
        val fragment = supportFragmentManager.findFragmentById(R.id.container)
        return if (selectedMenu == R.id.nav_transc_history && fragment is TransactionHistoryFragment) fragment else null
    }

    fun reInitNavigationMenu(position: Int) {
        onNavigationItemSelected(binding.navView.menu.getItem(position))
    }

    private fun changeOperator(): Fragment? {
        var deviceID = sharedPref().getString(SharedPref.DEVICE_ID)
        Timber.i("Change Operator. Device ID : $deviceID")

        AlertDialog.Builder(this)
            .setTitle(R.string.change_operator)
            .setMessage(R.string.are_you_sure)
            .setNegativeButton(R.string.cancel, null)
            .setPositiveButton(R.string.yes) { dialog, _ ->
                dialog.dismiss()
                val shiftOpen = getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
                shiftOpen?.openShiftId?.let {
                    salesService.logoutEmployeeCall(deviceID ?: "").awaitAsync({ resp ->
                        if (resp.isSuccessful) {
                            clearLoginSession()
                        } else {
                            if (resp.code() == 401) {
                                Timber.i("Change Operator returns 401, relogin...")
                                putData(SharedPref.LOGIN_ADMIN_STATUS, false)
                                clearJson(SharedPref.ADMIN_DATA)
                                val intent = Intent(this, LoginAdminActivity::class.java)
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                startActivity(intent)
                            } else {
                                showMessage(resp.message().ifBlank { "Gagal, mungkin ada kendala oleh Internet" })
                            }
                        }
                    }, {
//                            if(outletFeature().offlineMode){
//                                clearLoginSession(false)
//                            }
                    }, this, dialogMessage = "Logging out...")
                } ?: kotlin.run {
                    clearLoginSession()
                }
            }
            .show()
        return null
    }

    private fun clearLoginSession(isClearShiftSession: Boolean = true) {
        Bugsnag.leaveBreadcrumb("logout")
        putData(SharedPref.LOGIN_EMPLOYEE_STATUS, false)
        clearJson(SharedPref.EMPLOYEE_DATA)
        if (isClearShiftSession) clearJson(SharedPref.SHIFT_DATA)
        startActivity(Intent(this, ChooseOperatorActivity::class.java))
        finish()
    }

    private fun showSnackbar(msg: String, duration: Int = Snackbar.LENGTH_SHORT) {
        Timber.i("[[SNACKBAR]] $msg")
        val snack = Snackbar.make(binding.incAppBar.container, msg, duration)
        snack.setAction("OK") { snack.dismiss() }
        snack.show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Timber.i("onActivityResult, requestCode: $requestCode, resultCode: $resultCode")
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == RC_RESERVATION) {
                onNavigationItemSelected(binding.navView.menu.findItem(R.id.nav_transaction))
                val reservation = data?.extras?.getParcelable<ReservationEntity>("data")
                showSnackbar("Transaction as ${reservation?.name}")
                reservation?.takeIf { isTabLayout }?.let {
                    getTransactionMain()?.transactionFragment?.setReservationData(it)
                }
            }

            if(requestCode == CameraOrGalleryIntent.CAMERA || requestCode == CameraOrGalleryIntent.GALLERY ){

            }
        }
    }

    override fun onDestroy() {
//        printerManager.onStop()
        broadcastReceiver?.let { unregisterReceiver(it) }
        Timber.i("#onDestroy")
        super.onDestroy()
    }


    override fun supportFragmentInjector() = fragmentDispatchingInjector

//    override fun onSaveInstanceState(outState: Bundle) {
////        outState.apply {
////            putInt("selected", selectedMenu)
////        }
//        super.onSaveInstanceState(outState)
//    }

    override fun onSaveInstanceState(outState: Bundle, outPersistentState: PersistableBundle) {
        outState?.let { state ->
            val report =  TooLargeTool.bundleBreakdown(state)
            Timber.d("bundleBreakdown: $report")
        }
        super.onSaveInstanceState(outState, outPersistentState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState?.let { state ->
            val report =  TooLargeTool.bundleBreakdown(state)
            Timber.d("bundleBreakdown: $report")
        }
        super.onSaveInstanceState(outState)
    }

}
