package com.uniq.uniqpos.view.purchase

import androidx.lifecycle.viewModelScope
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.repository.PurchaseRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.PrintTask
import com.uniq.uniqpos.model.SnackbarEvent
import com.uniq.uniqpos.util.SingleLiveEvent
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.collections.ArrayList

/**
 * Created by annasblackhat on 14/02/19
 */

class OperationalCostViewModel
@Inject constructor(private val operationalCostRepository: PurchaseRepository,
                    private val settingRepository: SettingRepository) : BaseViewModel() {

    val taskSave = SingleLiveEvent<Boolean>()
    val taskShowDialog = SingleLiveEvent<Any>()
    val taskShowProgressDialog = SingleLiveEvent<Boolean>()
    val taskPrint = SingleLiveEvent<PrintTask>()
    val taskGeneratePrintFormat = SingleLiveEvent<Pair<OperationalCostEntity, List<PrinterEntity>>>()
    val taskRefreshOpCostNames = SingleLiveEvent<List<String>>()

    val supplierList = ArrayList<SupplierEntity>()
    val bankList = ArrayList<BankEntity>()
    val purchaseCategoryList = ArrayList<PurchaseReportCategoryEntity>()
    val operationalCostHistory = ArrayList<OperationalCostEntity>()

    val supplierLive = operationalCostRepository.getSupplierLive()
    val purcaseCategoryLive = operationalCostRepository.getPurchaseReportCategoryLive()
    val uniqueOpCostNameLive = operationalCostRepository.uniqueOpCostName

    fun getOperationalCostLive(outletId: Int?) = operationalCostRepository.getOperationalCostLive(outletId)
    fun getBankLive(outletId: Int?) = operationalCostRepository.getBankLive(outletId)

    fun loadOperationalCostNames() {
        viewModelScope.launch {
            val result = operationalCostRepository.getUniqueOpCostNameOnce()
            taskRefreshOpCostNames.postValue(result)
        }
    }

    fun saveOperationalCost(operationalCostEntity: OperationalCostEntity, isPrintReceipt: Boolean = true) {
        Timber.i("save operational cost.. ${operationalCostEntity.opcostName} | print $isPrintReceipt")
        viewModelScope.launch {
            try {
                operationalCostRepository.saveOperationalCost(operationalCostEntity)
                _snackbarText.postValue(Event(SnackbarEvent("Operational Cost Successfully Saved")))
                taskSave.postValue(true)
                if (isPrintReceipt) {
                    settingRepository.getPrinterList().filter { it.settingPrintreceipt == "1" }.takeIf { it.isNotEmpty() }?.let { printers ->
                        taskGeneratePrintFormat.postValue(Pair(operationalCostEntity, printers))
                    }
                }
                Timber.i("save success")
            } catch (e: Exception) {
                Timber.i("save failed $e")
                taskShowDialog.postValue(e)
            }
        }
    }

    fun saveSupplier(supplier: SupplierEntity) {
        viewModelScope.launch {
            try {
                operationalCostRepository.saveSupplier(supplier)
                taskSave.postValue(true)
            } catch (e: Exception) {
                taskShowDialog.postValue(e)
            }
        }
    }

    fun addPurchaseReportCategory(value: String) {
        //first check if its already exist in database
        if (purchaseCategoryList.any { it.name.equals(value, ignoreCase = true) }) {
            taskShowDialog.postValue("Purchase Report Category '$value' sudah pernah di tambahkan")
            return
        }

        viewModelScope.launch {
            try {
                taskShowProgressDialog.postValue(true)
                operationalCostRepository.savePurchaseReportCategory(value)
            } catch (e: Exception) {
                taskShowDialog.postValue(e)
            } finally {
                taskShowProgressDialog.postValue(false)
            }
        }
    }

    fun loadOperationalCostHistory() {
        viewModelScope.launch {
            operationalCostHistory.clear()
            operationalCostHistory.addAll(operationalCostRepository.getHistoryOperationalCost())
        }
    }


}