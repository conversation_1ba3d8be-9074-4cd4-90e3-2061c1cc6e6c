package com.uniq.uniqpos.view.transaction.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.animation.TranslateAnimation
import android.widget.FrameLayout
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.FragmentManager
import com.bugsnag.android.Bugsnag
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.PromotionEntity
import com.uniq.uniqpos.databinding.DialogVoucherDetailBinding
import com.uniq.uniqpos.databinding.ListItemPromoFreeBinding
import com.uniq.uniqpos.databinding.ListItemPromoFreeHeaderBinding
import com.uniq.uniqpos.model.LinkMenuProduct
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.PromotionFree
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.GlobalAdapterMultiView
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber


/**
 * Created by annasblackhat on 2019-08-13
 */
class VoucherDetailDialogFragment : BottomSheetDialogFragment() {

    private var viewModel: TransactionViewModel? = null
    private val promotionFreeList = ArrayList<PromotionFree>()
    private var promoType = ""
    private var isVoucherRemoved = false
    private var _binding: DialogVoucherDetailBinding? = null
    private val binding get() = _binding!!
    private var isShown = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogVoucherDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())

        isVoucherRemoved = false
        promoType = viewModel?.promoApplied?.firstOrNull()?.type ?: ""
        val typeId = viewModel?.promoApplied?.firstOrNull()?.typeId
        val isDealsSpecialPrice =
            typeId == Constant.PROMO_TYPE_DEALS && promoType == "special_price"

        Timber.d("[promo] promoType: $promoType | $typeId")
        if (promoType == "free" || isDealsSpecialPrice) {
            binding.recViewFree.adapter = promoItemAdapter()
        } else {
            val promoTerm = viewModel?.promoApplied?.firstOrNull()?.term
            val promoName = viewModel?.promoApplied?.firstOrNull()?.name
            setTextFromHtml(binding.txtDetail, generateContent())
            setTextFromHtml(binding.txtPromoTerms, promoTerm.safe())
            binding.txtTitle.text = promoName ?: "Detail Voucher"
            binding.txtPromoTerms.setVisible(promoTerm.removeHtmlTag().isNotBlank())
        }

        viewModel?.apply {
            promoApplied.firstOrNull()?.let { promo ->
                binding.txtVoucher.text = promo.displayVoucher
            }
        }

        setupView()
        view.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val dialog = dialog as BottomSheetDialog
                val bottomSheet =
                    dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                val behavior = BottomSheetBehavior.from(bottomSheet!!)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        })
    }

    private fun setupView() {
        val outletId = context?.outlet()?.outletId.safe()
        binding.btnRefresh.setOnClickListener {
            refreshBill()
            dismiss()
            context?.toast("Voucher Refreshed")
        }

        binding.btnRemove.setOnClickListener {
            context?.showMessage(
                "Are you sure?",
                "Confirmation",
                { _, _ ->
                    viewModel?.takeIf { it.promoApplied.isNotEmpty() }?.apply {
                        removePromotion(promoApplied.first(), outletId)
                        isVoucherRemoved = true
                        dismiss()
                    } ?: run { context?.showMessage("promotion not found!") }
                })
        }
    }

    fun check(vm: ViewDataBinding) {
        when (vm) {
            is ListItemPromoFreeBinding -> {

            }
        }
    }

    private fun promoItemAdapter(): GlobalAdapterMultiView {
        val typeId = viewModel?.promoApplied?.firstOrNull()?.typeId
        // val isDealsSpecialPrice = typeId == Constant.PROMO_TYPE_DEALS && promoType == "special_price"
        val isPromoFree = listOf(
            Constant.PROMO_TYPE_FREE,
            Constant.PROMO_TYPE_FREE_MEMBER,
            Constant.PROMO_TYPE_FREE_VOUCHER
        ).any { p -> p == typeId }

        promotionFreeList.clear()

        if (isPromoFree) {
            viewModel?.createPromotionFreeList()?.let { promotionFreeList.addAll(it) }
        } else {
            viewModel?.createPromotionItemList()?.let { promotionFreeList.addAll(it) }
        }

        return object : GlobalAdapterMultiView(R.layout.list_item_promo_free_header,
            R.layout.list_item_promo_free, promotionFreeList, { position ->
                if (position == 0) ViewType.TYPE_HEADER else ViewType.TYPE_ITEM
            }, { binding, position ->
                when (binding) {
                    is ListItemPromoFreeHeaderBinding -> {
                        binding.root.setOnClickListener {
                            if (binding.txtPromoDetail.visibility == View.GONE) {
                                binding.txtPromoDetail.visibility = View.VISIBLE
                                slideUp(binding.root)
                                binding.txtPromoDetail.post {
                                    binding.imgArrow.rotation = 90f
                                }
                            } else {
                                binding.txtPromoDetail.visibility = View.GONE
                                binding.txtPromoDetail.post {
                                    binding.imgArrow.rotation = 0f
                                }
                            }
                        }
                    }

                    is ListItemPromoFreeBinding -> {
                        binding.imgMin.setOnClickListener {
                            changeQtyFreeItem(-1, position, binding)
                        }
                        binding.imgAdd.setOnClickListener {
                            changeQtyFreeItem(1, position, binding)
                        }
                    }
                }
            }) {}
    }

    private fun changeQtyFreeItem(
        qty: Int,
        position: Int,
        holderBinding: ListItemPromoFreeBinding,
        showLinkMenu: Boolean = true
    ) {
        val result = holderBinding.txtQty.text.toString().safeToInt() + qty
        var maxFree = promotionFreeList.firstOrNull()?.qtyMax ?: 0
        val isUnlimited = promotionFreeList.firstOrNull()?.isUnlimitedItem.safe()
        Timber.d("[promo] changeQty: $qty | $result | $maxFree | $isUnlimited")
        if (result >= 0) {
            if (!isUnlimited && promotionFreeList.sumOf { it.qtySelected } + qty > maxFree) {
                context?.toast("Qty promo melebihi batas maksimal!")
            } else if (!termFulfilled()) {
                context?.toast("Syarat Pembelian Item belum terpenuhi!")
            } else {
                if (showLinkMenu && promotionFreeList[position].qtySelected == 0){
                    val linkMenu =
                        viewModel?.linkMenuList?.filter { it.productDetailFkid == promotionFreeList[position].productDetailFkid }
                    Timber.i("total Link Menu: ${linkMenu?.size}")
                    if (linkMenu?.isNotEmpty().safe()) {
                        showLinkMenu(qty, position, holderBinding)
                        return
                    }
                }
                holderBinding.txtQty.text = (result).toString()
                promotionFreeList[position].qtySelected = result
            }
        } else {
            context?.toast("Error adding minus item")
        }
    }

    private fun showLinkMenu(qty: Int,
                             position: Int,
                             holderBinding: ListItemPromoFreeBinding){
        val productEntity = viewModel?.products?.firstOrNull { it.productDetailId ==  promotionFreeList[position].productDetailFkid}
        if(productEntity == null){
            context?.showMessage("Can not find the product", "Failed")
            return
        }
        object : LinkMenuDialog(requireContext(), viewModel!!) {
            override fun addToBill(
                productEntity: ProductEntity,
                linkMenuSelected: List<LinkMenuProduct>,
                qty: Int
            ) {
                changeQtyFreeItem(qty, position, holderBinding, false)

                val extraList = ArrayList<Order>()
                linkMenuSelected.forEach {
                    extraList.add(Order(
                        it.product.copy(priceSell = it.linkMenuDetail.priceAdd),
                        extra = ArrayList(),
                        employeeId = context?.employee()?.employeeId ?: 0,
                        extraType = "link"
                    ))
                }
                promotionFreeList[position].extra = extraList
            }
        }.showLinkMenu(productEntity)
    }

    private fun termFulfilled(): Boolean {
        var isFulfilled = viewModel?.isPromoTermFulfilled(
            PromotionEntity(
                promotionProduct = promotionFreeList.firstOrNull()?.productTerms ?: ArrayList()
            )
        )
        return isFulfilled.safe()
    }

    fun slideUp(view: View) {
//        view.visibility = View.VISIBLE
        val animate = TranslateAnimation(
            0f, // fromXDelta
            0f, // toXDelta
            view.height.toFloat(), // fromYDelta
            0f
        )                // toYDelta
        animate.duration = 100
        animate.fillAfter = true
        view.startAnimation(animate)
    }

    fun setViewModel(viewModel: TransactionViewModel) {
        this.viewModel = viewModel
    }

    private fun generateContent(): String {
        return viewModel?.let { viewModel ->
            Timber.d("promo applied : ${Gson().toJson(viewModel.promoApplied)}")
            var result = ""

            viewModel.promoApplied.firstOrNull()?.let { promo ->
                when (promo.type) {
                    "free" -> {
                        result += "<b> Dapatkan item berikut secara Gratis </b> : <br/>"
                        promo.promotionDetail?.productsFree?.forEach { product ->
                            viewModel.products.firstOrNull { it.productDetailId == product.productDetailFkid }
                                ?.let {
                                    result += "- ${it.name}  (${product.qty}) <br/>"
                                }
                        }

                        result += "<br/><br/><b>Dengan membeli item berikut : </b><br/>"
                        promo.promotionDetail?.products?.forEach { product ->
                            viewModel.products.firstOrNull { it.productDetailId == product.productDetailFkid }
                                ?.let {
                                    result += "- ${it.name}  (${product.qty}) <br/>"
                                }
                        }
                    }

                    "special_price" -> {
                        result += "<b>Harga Spesial Untuk Item :</b> <br/>"
                        val minOrderInfo =
                            if (promo.minOrder > 0) "dengan minimum order Rp${promo.minOrder.toCurrency()}" else "tanpa minimum order"
                        promo.promotionDetail?.products?.forEach { productSpecial ->
                            val product =
                                viewModel.products.firstOrNull { it.productDetailId == productSpecial.productDetailFkid }
                            result += "<p>- ${product?.name} &emsp;  <strike>${product?.priceSell?.toCurrency()}</strike>  &emsp;  ${productSpecial.price.toCurrency()} </p>"
                        }
                        result += "<i>$minOrderInfo</i>"
                    }

                    "discount" -> {
                        val minOrderInfo =
                            if (promo.minOrder > 0) "dengan minimum order Rp${promo.minOrder.toCurrency()}" else "tanpa minimum order"
                        if (promo.promotionDetail?.products.isNullOrEmpty()) {
                            result += if (promo.promoDiscountType == "nominal") {
                                "Voucher Senilai   <b>Rp${
                                    promo.value?.safeToInt().toCurrency()
                                }</b>, berlaku untuk semua item"
                            } else {
                                val maxDisc =
                                    if (promo.promoDiscountMaximum > 0) "<br/>Maksimal diskon Rp${promo.promoDiscountMaximum.toCurrency()}" else ""
                                "Discount <b>${promo.value?.safeToInt()}%</b>, berlaku untuk semua item$maxDisc"
                            }
                        } else {
                            if (promo.discountType == Constant.PROMO_DISC_TYPE_NOTA || (promo.typeId == Constant.PROMO_TYPE_DEALS && promoType == "discount")) {
                                val value =
                                    if (promo.promoDiscountType == "percent") "${promo.value}%" else "Rp${
                                        promo.value.safeToInt().toCurrency()
                                    }"
//                                result += "<b>Diskon $value, berlaku untuk ${promo.promotionDetail?.products?.size.safe()} item :</b> <br/>"
                                val maxPromo =
                                    if (promo.promoDiscountMaximum > 0) " (maksimum ${promo.promoDiscountMaximum.toCurrency()}) " else ""
                                result += "<b>Diskon ${value}${maxPromo}, berlaku untuk semua item</b> <br/>"
//                                promo.promotionDetail?.products?.forEach { productPromo ->
//                                    val product =
//                                        viewModel.products.firstOrNull { it.productDetailId == productPromo.productDetailFkid }
//                                    result += "<p> - ${product?.name} </p>"
//                                }
                                promo.promotionDetail?.products?.filter { it.type == Constant.PROMO_PRODUCT_ORDER }
                                    ?.takeIf { it.isNotEmpty() }?.let { productTerm ->
                                    result += "<b>Syarat item yang harus di beli</b> (min ${productTerm[0].qty}) : <br/>"
                                    productTerm.forEach { product ->
                                        viewModel.products.firstOrNull { it.productDetailId == product.productDetailFkid }
                                            ?.let {
                                                result += " - ${it.name} <br/>"
                                            }
                                    }
                                }
                                Timber.d("[promo] deals product: ${Gson().toJson(promo.promotionDetail?.products)}")
                            } else {
                                result += "<b>Diskon Untuk Item :</b> <br/>"
                                promo.promotionDetail?.products?.filter { it.type != Constant.PROMO_PRODUCT_ORDER }
                                    ?.forEach { productPromo ->
                                        val suffix = if (productPromo.isPercent == 1) "%" else ""
                                        val prefix = if (productPromo.isPercent == 0) "Rp" else ""
                                        val product =
                                            viewModel.products.firstOrNull { it.productDetailId == productPromo.productDetailFkid }
                                        result += "<p>- ${product?.name} <br/> discount $prefix${productPromo.amount.toCurrency()}$suffix </p>"
                                    }
                            }
                        }
                        result += "<br/> <i>$minOrderInfo</i>"
                    }

                    else -> {
                        val minOrderInfo =
                            if (promo.minOrder > 0) "dengan minimum order Rp${promo.minOrder.toCurrency()}" else "tanpa minimum order"
                        result += "Voucher Senilai   <b>Rp${
                            promo.value?.safeToInt().toCurrency()
                        } </b><br/> <i>$minOrderInfo</i>"
                    }
                }
            }
            Timber.d("promo content: \n$result")
            result
        } ?: run { "free item not described" }
    }

    private fun addFreeItem() {
        if (!isVoucherRemoved) viewModel?.addFreeItem(promotionFreeList)
    }

    private fun refreshBill() {
        val typeId = viewModel?.promoApplied?.firstOrNull()?.typeId
        val isDealsSpecialPrice =
            typeId == Constant.PROMO_TYPE_DEALS && promoType == "special_price"
        val isDealsDiscount = typeId == Constant.PROMO_TYPE_DEALS && promoType == "discount"

        if (!isVoucherRemoved) {
            if (isDealsSpecialPrice) {
                viewModel?.addPromoItem(promotionFreeList)
            } else if (isDealsDiscount) {
                viewModel?.reCalculateTax()
                viewModel?.taskRefreshTax?.call()
            } else {
                viewModel?.promoApplied?.firstOrNull { it.type == "free" }?.let { addFreeItem() }
            }
        }
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if(isShown) {
            context?.toast("this dialog already shown")
            return
        }
        if (!isAdded) super.show(manager, tag)
        isShown = true
    }

    override fun onCancel(dialog: DialogInterface) {
        refreshBill()
        super.onCancel(dialog)
        isShown = false
    }

    override fun onDismiss(dialog: DialogInterface) {
        refreshBill()
        super.onDismiss(dialog)
        isShown = false
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }
}