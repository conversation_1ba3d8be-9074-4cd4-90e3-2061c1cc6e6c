package com.uniq.uniqpos.view.transaction.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.databinding.DialogVoidBinding
import com.uniq.uniqpos.model.Discount
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber

/**
 * Created by annas<PERSON>ck<PERSON> on 11/07/18
 */
class VoidDialog(mContext: Context,
                 val order: Order,
                 val employee: Employee,
                 val viewModel: TransactionViewModel) : AlertDialog(mContext) {

    private lateinit var binding: DialogVoidBinding
    private var voidListener: () -> Unit = {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_void, null, false)
        setContentView(binding.root)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)

        binding.btnSubmit.setOnClickListener {
            if (Utils.isValidField(binding.edtInfo, binding.edtQty)) {
                if (binding.edtQty.toInt() > (order.holdQty - order.voidedQty)) {
                    context.toast("Jumlah item terlalu besar!")
                } else {
                    dismiss()
                    order.voidedQty += binding.edtQty.toInt()

                    var discount = order.discount.copy()
                    if(discount.discountType == Constant.TYPE_NOMINAL){
                        discount = Discount()
                    }

                    val orderVoid = order.copy(qty = binding.edtQty.toInt(), isItemVoid = true, voidInfo = binding.edtInfo.value().trim(),
                            extra = ArrayList(), printed = binding.edtQty.toInt(), tmpId = System.currentTimeMillis(), employeeId = context.employee()?.employeeId
                            ?: 0, voidEmployeeAuthId = employee.employeeId, voidParentId = order.tmpId, discount = discount)
                    orderVoid.promotion = order.promotion?.copy()
                    order.extra.forEach { extra ->
                        val extraVoid = extra.copy(voidParentId = extra.tmpId, isItemVoid = true, qty = binding.edtQty.toInt(), tmpId = System.currentTimeMillis())
                        for (i in 0 until binding.edtQty.toInt()) {
//                            viewModel.updateTax(order, viewModel.calculateDiscPerItem(order, order), false)
                        }
                        viewModel.updatePrice(extraVoid)
                        orderVoid.extra.add(extraVoid)
                    }

                    viewModel.updatePrice(orderVoid)
                    viewModel.orders.add(orderVoid)
                    viewModel.reCalculateTax()
                    viewModel.updatePromotionValue(orderVoid)

                    //print
                    val outlet = context.outlet()
                    val employee = context.employee()
                    if (outlet != null && employee != null) {
                        viewModel.generatePrintFormat(SalesEntity(viewModel.salesEdit?.noNota.safe(), orderList = arrayListOf(viewModel.orders.last().copy(printed = 0)), customer = viewModel.salesEdit?.customer), outlet, employee, true)
                    } else {
                        Timber.i("can not print... outlet or employee is null. outlet : ${outlet?.outletId} | employee ${employee?.employeeId}")
                    }
                    voidListener()
                }
            }
        }

        binding.btnCancel.setOnClickListener { dismiss() }
    }

    fun setOnVoidSaved(voidListener: () -> Unit): VoidDialog {
        this.voidListener = voidListener
        return this
    }
}