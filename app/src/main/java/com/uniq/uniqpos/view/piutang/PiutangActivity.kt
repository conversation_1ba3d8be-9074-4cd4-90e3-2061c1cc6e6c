package com.uniq.uniqpos.view.piutang

import android.app.SearchManager
import android.content.Context
import androidx.lifecycle.Observer
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.FrameLayout
import android.widget.SearchView
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.ActivityPiutangBinding
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.view.global.BaseActivity
import timber.log.Timber

class PiutangActivity : BaseActivity<PiutangViewModel, ActivityPiutangBinding>() {

    var isUseTabLayout = false
        private set
    private val piutangListFragment = PiutangListFragment()

    private lateinit var searchView: SearchView
    private lateinit var searchListener: SearchView.OnQueryTextListener

    override fun getLayoutRes() = R.layout.activity_piutang
    override fun getViewModel() = PiutangViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        if (resources.getBoolean(R.bool.landscape_only)) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
        }

        findViewById<FrameLayout>(R.id.frame_detail)?.let {
            isUseTabLayout = true
        }

        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                    .replace(R.id.frame_list, piutangListFragment)
                    .commit()

            if (isUseTabLayout) {
                supportFragmentManager.beginTransaction()
                        .replace(R.id.frame_detail, PiutangDetailFragment())
                        .commit()
            }
        }

        searchListener = object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(q: String?): Boolean {
                viewModel.searchData(q)
                return true
            }
        }

        observeData()
    }

    override fun observeData() {
        viewModel.getPiutangLive(outlet()?.outletId)
                .observe(this, { result ->
                    viewModel.piutangList.clear()
                    result?.data?.let { piutangList ->
                        piutangList.filter { it.unpaid > 0 }.let { viewModel.piutangList.addAll(it) }
                        piutangList.filter { it.unpaid <= 0 }.let { viewModel.piutangList.addAll(it) }
                    }
                    viewModel.findSales()
                })
    }

    fun onItemSelected(position: Int) {
        if (position >= viewModel.piutangListShow.size) return
        val bundle = Bundle()
        bundle.putParcelable("data", viewModel.piutangListShow[position])
        bundle.putParcelable("sales", viewModel.piutangListShow[position].sales)
        if (isUseTabLayout) {
            val fragment = PiutangDetailFragment()
            fragment.arguments = bundle
            supportFragmentManager.beginTransaction()
                    .replace(R.id.frame_detail, fragment)
                    .commit()
        } else {
            val intent = Intent(this, PiutangDetailActivity::class.java)
            intent.putExtra("data", bundle)
            startActivity(intent)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_transaction_cart, menu)
        val searchManager = getSystemService(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu?.findItem(R.id.action_search)?.actionView as SearchView
        searchView.setSearchableInfo(searchManager.getSearchableInfo(componentName))
        searchView.setOnQueryTextListener(searchListener)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
