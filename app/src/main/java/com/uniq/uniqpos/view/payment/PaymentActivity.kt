package com.uniq.uniqpos.view.payment

import android.app.Activity
import android.app.AlertDialog
import android.content.ClipboardManager
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.bugsnag.android.Bugsnag
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.databinding.*
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupToast
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import kotlinx.coroutines.*
import smartdevelop.ir.eram.showcaseviewlib.GuideView
import smartdevelop.ir.eram.showcaseviewlib.config.DismissType
import timber.log.Timber
import java.util.*
import kotlin.collections.ArrayList

class PaymentActivity : BaseActivity<PaymentViewModel, ActivityPaymentBinding>() {

    private val RC_SPLITBILL: Int = 1
    private lateinit var employee: Employee
    private lateinit var outlet: Outlet
    private lateinit var shift: ShiftOpen
    private var cardDialog: AlertDialog? = null
    private var selectedPayment = 0

    private lateinit var vDialogCard: DialogCardPaymentBinding

    private var paymentFlow = ""
    private var accountNumber = ""
    private var isPiutang = false
    private var isAfterSplit = false
    private var isNoteListEnable = false
    private val paymentHelper = ArrayList<Int>()

    companion object {
        const val INTENT_PAY_FLOW = "pay-flow"
    }

    override fun getLayoutRes() = R.layout.activity_payment
    override fun getViewModel() = PaymentViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
//        if (resources.getBoolean(R.bool.landscape_only)) {
////            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
//        }

        super.onCreate(savedInstanceState)
    }

    override fun initView() {
        super.initView()
        viewModel.loadReceiptReceiver()

        employee = getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java) ?: Employee()
        outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
        shift = getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java) ?: ShiftOpen()

        binding.edtReceiptAddress.setText(intent.getStringExtra("receipt_receiver"))
        (intent.getSerializableExtra("merge_ids") as? ArrayList<String>)?.let {
            viewModel.mergeIds.addAll(
                it
            )
        }
        paymentFlow = intent.getStringExtra(INTENT_PAY_FLOW) ?: ""
        isPiutang = intent.getBooleanExtra("ispiutang", false)

        Timber.i("intent: sales? ${intent.hasExtra("sales")} | sale-json? ${intent.hasExtra("sales-json")}")
        if (intent.hasExtra("sales")) {
            Bugsnag.leaveBreadcrumb("intent: sales")
            viewModel.sales = intent.getParcelableExtra("sales") as? SalesEntity ?: SalesEntity("")
        }else if(intent.hasExtra("sales-json-static")){
            Bugsnag.leaveBreadcrumb("intent: sales-json-static")
            DataHolder.getData()?.let { salesJson ->
                viewModel.sales = Gson().fromJson(salesJson, SalesEntity::class.java)
            } ?: run {
                showMessage("no sales data...")
            }
        }  else if (intent.hasExtra("sales-json")) {
            Bugsnag.leaveBreadcrumb("intent: sales-json")
            intent.getStringExtra("sales-json")?.let { salesJon ->
                viewModel.sales = Gson().fromJson(salesJon, SalesEntity::class.java)
            } ?: kotlin.run {
                showMessage("no sales...")
            }
        } else if(!isPiutang) {
            toast("no item on sales", level = Level.ERROR)
            finish()
            return
        }

        viewModel.salesOriginal = intent.getParcelableExtra("sales_original") as? SalesEntity
        viewModel.salesOriginal?.let {
            Timber.i("Sales Original : ${Gson().toJson(viewModel.salesOriginal)}")
            binding.btnSplit.setTextColor(Utils.getColor(this, R.color.text_grey))
            binding.btnSplit.background.setColorFilter(
                ContextCompat.getColor(
                    this,
                    R.color.background
                ), PorterDuff.Mode.SRC_ATOP
            )
            binding.btnSplit.isEnabled = false
        }

        if (isPiutang) {
            viewModel.paymentList = arrayListOf("CASH", "CARD")
            viewModel.piutang = intent.getParcelableExtra("piutang")
            binding.txtGrandTotal.text = viewModel.piutang?.unpaid?.toCurrency()
            binding.btnSplit.visibility = View.GONE
            binding.txtChangeKet.text = "Sisa Piutang"
        }

        binding.txtGrandTotal.text = viewModel.getGrandTotal().toCurrency()

        initCardDialog()
        //init payment enable by setting from web
        outlet()?.let { outlet ->
            val paymentListMap = mapOf(
                "CASH" to outlet.paymentCash,
                "CARD" to outlet.paymentCard,
                "COMPLIMENT" to outlet.paymentCompliment,
                "PIUTANG" to outlet.paymentPiutang,
                "DUTY MEALS" to outlet.paymentDuty
            )
            for ((pay, enable) in paymentListMap) {
                if (enable == 0) viewModel.paymentList.remove(pay)
            }

            //if card is only payment that's available, then show the bank option
            if (viewModel.paymentList.size == 1 && viewModel.paymentList[0] == "CARD") {
                countChange()
                cardDialog?.show()
            } else if (viewModel.paymentList.isEmpty()) {
                val url = "${BuildConfig.WEB_URL}outlets/paymentmedia"
                showMessage(
                    "No payment method enabled! Please enable it from Back Office. \nVisit : $url",
                    positiveMsg = "ENABLE NOW",
                    positiveAction = { _, _ ->
                        launchUrl(url)
                    })
            } else {
            }
        }

        viewModel.paymentList.forEachIndexed { index, _ ->
            viewModel.paymentStatus[index] = false
            viewModel.paymentValue[index] = 0
        }

        binding.btnSplit.setOnClickListener {
            if (viewModel.sales.orderList?.size == 1 && viewModel.sales.orderList!![0].qty <= 1) {
                showMessage(getString(R.string.qty_impossible_split_bill))
            } else if (viewModel.mergeIds.isNotEmpty()) {
                showMessage(
                    "Setelah melakukan 'MERGE BILL' anda harus menyimpan terlebih dahulu transaksi tersebut! \n" +
                            "Setelah itu anda bisa melakukan SPLIT BILL\n\n" +
                            "Silahkan kembali untuk menyimpan transaksi",
                    "WARNING",
                    DialogInterface.OnClickListener { _, _ ->
                        finish()
                    },
                    positiveMsg = "KEMBALI"
                )
            } else {
                var msg: String? = null
                //if there is any promotion applied, warn to user
                if (viewModel.sales.orderList?.any { it.promotion != null } == true) {
                    msg =
                        "Split Bill untuk transaksi dengan promo masih belum stabil. Tetap lanjutkan untuk Split Bill?"
                    showMessage("Split Bill belum dapat dilakukan untuk transaksi dengan promo")
                    return@setOnClickListener
                } else {
                    msg = when {
                        (viewModel.sales.discount?.discount.safe() > 0) && (viewModel.sales.discount?.voucher.safe() > 0) -> "Diskon dan Voucher"
                        viewModel.sales.discount?.discount.safe() > 0 -> "Diskon"
                        viewModel.sales.discount?.voucher.safe() > 0 -> "Voucher"
                        else -> null
                    }
                    msg?.let {
                        msg =
                            "$it yang telah anda inputkan akan di HAPUS dari transaksi. Lanjutkan?"
                    }
                }
                msg?.let {
                    showMessage(msg, "Split Bill", { _, _ ->
                        navigateToSplitBill()
                    })
                } ?: kotlin.run { navigateToSplitBill() }
            }
        }

        binding.imgClearReceipt.setOnClickListener {
            binding.edtReceiptAddress.setText("")
            Timber.d("[PAYMENT] clear....")
        }

        Timber.i("available payment options : ${Gson().toJson(viewModel.paymentList)}")

        Bugsnag.leaveBreadcrumb("initLayout")
        initLayout()
        Bugsnag.leaveBreadcrumb("initViewFeature")
        initViewFeature()
        Bugsnag.leaveBreadcrumb("showCase")
        showCase()
    }

    private fun initClipboard() {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        clipboard.primaryClip?.getItemAt(0)?.text?.let { clipText ->
            val clipClean =
                clipText.toString().trim().replace(" ", "").replace("+", "").replace("-", "")
            val contact = when {
                clipClean.isPhoneNumber() -> {
                    binding.txtPasteInfo.text = "paste the number     "
                    clipClean.takeMax(15)
                }
                clipText.toString().isValidEmail() -> {
                    binding.txtPasteInfo.text = "paste the address     "
                    clipText.toString()
                }
                else -> null
            }

            contact?.let {
                binding.txtClipNumber.text = contact
                binding.layoutPaste.setVisible(true)

                Firebase.analytics
                    .logEvent(
                        "app_features",
                        bundleOf(
                            "Outlet" to "FCC:${outlet.outletId}:${outlet.name}",
                            "Feature" to "Found Contact Clipboard"
                        )
                    )
            }
        }
    }

    override fun observeData() {
        viewModel.getBankMedia(outlet()?.outletId)
            .observe(this) {
                viewModel.bankList.clear()
                it?.data?.let { data -> viewModel.bankList.addAll(data) }
                vDialogCard.recViewCard.adapter?.notifyDataSetChanged()
                var banks = ""
                viewModel.bankList.forEach { bank -> banks += "(${bank.bankId}) ${bank.name} ___ " }

                vDialogCard.txtBankCount.text =
                    getString(R.string.bank_accounts, viewModel.bankList.size)
                Timber.i("Bank List : $banks")
            }
    }

    override fun observeTask() {
        setupToast(this, viewModel.toastMsg)
        setupToastMessage(this, viewModel.toastMessage)
        setupDialogMessage(this, viewModel.dialogMsg)

        viewModel.finishTaskCommand.observe(this) {
            finish()
        }

        viewModel.cardDialogTaskCommand.observe(this) {
            cardDialog?.show()
        }

        viewModel.pDialogTaskCommand.observe(this) { isShow ->
            showDialog(isShow ?: false)
        }

        viewModel.paymentSavedTask.observe(this) { sales ->
            Firebase.analytics
                .logEvent(
                    FirebaseAnalytics.Event.PURCHASE,
                    bundleOf(
                        FirebaseAnalytics.Param.CURRENCY to "IDR",
                        FirebaseAnalytics.Param.VALUE to sales.grandTotal.toString()
                    )
                )

            val bundle = Bundle()
            bundle.putParcelable("sales", sales)
            bundle.putSerializable("isPayment", true)
            bundle.putSerializable("marge_ids", viewModel.mergeIds)
            val intent = Intent()
            intent.putExtras(bundle)
            setResult(Activity.RESULT_OK, intent)

            if (!viewModel.isOpeningCashDrawer) {
                Timber.i("payment finish...")
                finish()
            } else {
                Timber.i("System is currently trying to open cash drawer. Wait for 10 seconds...")
                //wait until cash drawer opening progress is finished
                GlobalScope.launch(Dispatchers.Main) {
                    withTimeoutOrNull(7000L) {
                        do {
                            Timber.i("wait, opening cashdrawer")
                            delay(500)
                        } while (viewModel.isOpeningCashDrawer)
                    }
                    showDialog(false)
                    finish()
                }
            }
        }

        viewModel.printTask.observe(this) { printTask ->
            printTask?.data?.let { printWifi ->
                Bugsnag.leaveBreadcrumb("printing receipt in payment")
                toast("printing...")
                managePrintWifi(printWifi)
                showDialog(false)
                if (printTask.isExitAfterPrint) {
                    setResult(Activity.RESULT_OK)
                    finish()
                }
            }
        }

        viewModel.openCashDrawerTask.observe(this) { printerList ->
            printerList?.takeIf { it.isNotEmpty() }?.let {
                val hasAccess = role().bukalaciuang
                Timber.i("has access open cash drawer: $hasAccess")
                if (hasAccess) {
                    Bugsnag.leaveBreadcrumb("opening cash drawer")
                    toast(getString(R.string.opening_cash_drawer))
                    showDialog(true, getString(R.string.open_cash_drawer))
                    viewModel.isOpeningCashDrawer = true

                    GlobalScope.launch {
                        withTimeoutOrNull(5000L) {
                            openCashDrawer(printerList) { response ->
                                Timber.i("opening cash drawer status: ${response.status} - message: ${response.message}")
                            }
                        }
                        viewModel.isOpeningCashDrawer = false
                    }
                }
            }
        }

        viewModel.receiptListRefresh.observe(this) {
            if (viewModel.receiptReceiverList.isNotEmpty())
                binding.edtReceiptAddress.setAdapter(
                    ArrayAdapter(
                        this,
                        android.R.layout.simple_list_item_1,
                        viewModel.receiptReceiverList
                    )
                )
        }
    }

    private fun initViewFeature() {
        outletFeature().let { feature ->
            binding.btnSplit.visibility = (feature.splitbill).visibility()
            isNoteListEnable = feature.notelist
        }
    }

    private fun showCase() {
        val employee = employee()
        val splitBillKey = "split_bill-${employee?.employeeId}"
        if (getLocalDataBoolean(splitBillKey)) {
            GuideView.Builder(this)
                .setTitle("Split Bill")
                .setTitleTextSize(17)
                .setContentText("Kamu bisa melakukan Split Bill dengan mengklik tombol ini. \nPastikan jumlah item yang di beli lebih dari satu ya!")
                .setTargetView(binding.btnSplit)
                .setDismissType(DismissType.anywhere)
                .build()
                .show()
            putData(splitBillKey, true)
        }
    }

    private fun navigateToSplitBill() {
        Firebase.analytics
            .logEvent(
                "split_bill",
                bundleOf("Outlet" to "FCC:${outlet.outletId}:${outlet.name}")
            )
        Timber.i("split bill for salesId ${viewModel.sales.noNota}")

        val intent = Intent(this, SplitBillActivity::class.java)
        intent.putExtra("sales", viewModel.sales.copy(discount = null))
        intent.putExtra(INTENT_PAY_FLOW, paymentFlow)
        startActivityForResult(intent, RC_SPLITBILL)
    }

    private fun initLayout() {
        viewModel.paymentList.firstOrNull()?.takeIf { it == "CARD" }?.let {
            listOf(binding.txtCardDetail, binding.layoutCardInfo).forEach { view ->
                view.visibility = View.VISIBLE
            }
        }

        binding.recViewPayment.adapter =
            object : GlobalAdapter<ListItemPaymentBinding>(
                R.layout.list_item_payment,
                viewModel.paymentList
            ) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemPaymentBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
                    if (holder.adapterPosition == selectedPayment) {
                        holder.binding.layoutPayment.setBackgroundColor(Color.parseColor("#333D44"))
                    } else {
                        holder.binding.layoutPayment.setBackgroundColor(Color.TRANSPARENT)
                        holder.binding.imgPayment.setColorFilter(0)
                    }

                    if (viewModel.paymentStatus[holder.adapterPosition] == true) {
                        holder.binding.imgPayment.setColorFilter(0)
                    } else {
                        holder.binding.imgPayment.setColorFilter(Color.parseColor("#515A61"))
                    }

                    holder.itemView.setOnClickListener { changePaymentSelected(holder.adapterPosition) }
                }
            }

        binding.edtPay.liveToCurrencyAndWatch {
            if (it.fromCurrency() <= 0 && viewModel.paymentStatus[selectedPayment] == true) {
                viewModel.paymentStatus[selectedPayment] = false
                binding.recViewPayment.adapter?.notifyItemChanged(selectedPayment)
            } else if (it.fromCurrency() > 0 && viewModel.paymentStatus[selectedPayment] == false) {
                viewModel.paymentStatus[selectedPayment] = true
                binding.recViewPayment.adapter?.notifyItemChanged(selectedPayment)
            } else if (it == "0" && binding.txtGrandTotal.text.toString() == "0") {
                viewModel.paymentStatus[selectedPayment] = true
                binding.recViewPayment.adapter?.notifyItemChanged(selectedPayment)
            }
            viewModel.paymentValue[selectedPayment] = it.fromCurrency()
            countChange()
        }

        binding.edtDueDate.setAsDateinput(supportFragmentManager, minDate = Calendar.getInstance())

        binding.recviewPaymentNominal.adapter = object :
            GlobalAdapter<ItemPaymentNominalBinding>(R.layout.item_payment_nominal, paymentHelper) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ItemPaymentNominalBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.itemView.setOnClickListener {
                    binding.edtPay.setText(paymentHelper[position].toCurrency())
                    Timber.i("click payment option: ${paymentHelper[position]}")

                    if (viewModel.paymentList[selectedPayment] == "CARD" && (viewModel.selectedBank < 0 || viewModel.bankList.getSafe(
                            viewModel.selectedBank
                        )?.accountNumber.isNullOrEmpty())
                    ) {
                        cardDialog?.show()
                    }

                    Firebase.analytics
                        .logEvent(
                            "app_features",
                            bundleOf(
                                "Feature" to "Payment Option",
                                "Outlet" to "PO:${outlet.outletId}:${outlet.name}"
                            )
                        )
                }
            }
        }

        binding.layoutPaste.setOnClickListener {
//            binding.edtReceiptSend.setText(binding.txtClipNumber.text.toString())
            binding.edtReceiptAddress.setText(binding.txtClipNumber.text.toString())

            Firebase.analytics
                .logEvent(
                    "app_features",
                    bundleOf(
                        "Feature" to "Paste Contact Clipboard",
                        "Outlet" to "PCC:${outlet.outletId}:${outlet.name}"
                    )
                )
        }

        binding.txtChangeCard.setOnClickListener { cardDialog?.show() }
        refreshPaymentOptions()
    }

    private fun refreshPaymentOptions() {
        var total = 0
        viewModel.paymentValue.forEach { total += it.value }

        if ((total == 0) || (total > viewModel.getGrandTotal())) {
            total = viewModel.getGrandTotal()
        }

        viewModel.piutang?.takeIf { total == 0 }?.let { total = it.unpaid }

        paymentHelper.clear()
        paymentHelper.addAll(viewModel.generatePaymentOptions(total))
        binding.recviewPaymentNominal.adapter?.notifyDataSetChanged()
    }

    private fun changePaymentSelected(position: Int) {
        Timber.i("select payment: ${viewModel.paymentList[position]} at $position")
        val hasPermission = when (viewModel.paymentList[position].lowercase()) {
            "compliment" -> role().compliment
            "duty meals" -> role().duty
            else -> true
        }
        if (!hasPermission) {
            toast(getString(R.string.no_permission))
            return
        }

        if (viewModel.isForbidMultiPayment()) {
            val selectedPayment = viewModel.getSelectedPaymentMethod().joinToString(", ")
            val forbidMultiPayment =
                viewModel.forbiddenMultiPayment.filter { selectedPayment.contains(it) }
                    .joinToString(", ")
            if (!viewModel.forbiddenMultiPayment.any { it == viewModel.paymentList[position] }) {
                showMessage("Pembayaran $forbidMultiPayment tidak dapat digabungkan dengan metode pembayaran lainnya")
            }
            if (!selectedPayment.contains(viewModel.paymentList[position])) {
                return
            }
        }

        binding.layoutPayInfo.visibility =
            if (viewModel.paymentList[position] == "COMPLIMENT" || viewModel.paymentList[position] == "PIUTANG") View.VISIBLE else View.GONE
        binding.layoutDueDate.visibility =
            if (viewModel.paymentList[position] == "PIUTANG") View.VISIBLE else View.GONE
        listOf(binding.txtCardDetail, binding.layoutCardInfo).forEach { view ->
            view.visibility =
                if (viewModel.paymentList[position] == "CARD") View.VISIBLE else View.GONE
        }

        val tmpSelected = selectedPayment
        selectedPayment = position
        binding.recViewPayment.adapter?.notifyItemChanged(tmpSelected)
        binding.recViewPayment.adapter?.notifyItemChanged(position)
        binding.edtPay.setText(
            if ((viewModel.paymentValue[position]
                    ?: 0) > 0
            ) viewModel.paymentValue[position].toString() else ""
        )

        if (viewModel.paymentList[position] == "CARD") {
            if (viewModel.selectedBank < 0 || viewModel.bankList.getSafe(viewModel.selectedBank)?.accountNumber.isNullOrEmpty())
                cardDialog?.show()
        } else {
            refreshPaymentOptions()
        }

    }

    private fun initCardDialog() {
        vDialogCard = DialogCardPaymentBinding.inflate(layoutInflater, null, false)
        cardDialog = AlertDialog.Builder(this)
            .setView(vDialogCard.root)
            .setCancelable(false)
            .create()
        cardDialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        vDialogCard.recViewCard.adapter =
            object : GlobalAdapter<ListItemCardPaymentBinding>(
                R.layout.list_item_card_payment,
                viewModel.bankList
            ) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemCardPaymentBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
                    holder.binding.rbCard.isChecked =
                        holder.adapterPosition == viewModel.selectedBank
                    holder.binding.rbCard.setOnClickListener {
                        toast("You choose : ${viewModel.bankList[holder.adapterPosition].name}")
                        val tmpSelected = viewModel.selectedBank
                        viewModel.selectedBank = holder.adapterPosition
                        notifyItemChanged(tmpSelected)
                    }
                }
            }

        vDialogCard.btnCancel.setOnClickListener {
            if (viewModel.paymentStatus[selectedPayment] == false) changePaymentSelected(0)
            vDialogCard.cardNumber.setText(accountNumber)
            cardDialog?.dismiss()
        }
        vDialogCard.btnSubmit.setOnClickListener {
            Timber.i("submit card clicked... no : ${vDialogCard.cardNumber.text.toString()}")
            if (Utils.isValidField(vDialogCard.cardNumber)) {
                if (viewModel.selectedBank >= 0) {
                    accountNumber = vDialogCard.cardNumber.value().trim()
                    viewModel.bankList[viewModel.selectedBank].accountNumber = accountNumber
                    cardDialog?.dismiss()
                    Timber.i("payment with bank successfully selected. ${viewModel.bankList[viewModel.selectedBank]} - $accountNumber")

                    binding.txtCardInfo.text =
                        "${viewModel.bankList[viewModel.selectedBank].name} - $accountNumber"
                    if (binding.txtChange.text.toString().fromCurrency() < 0)
                        binding.edtPay.setText(
                            binding.txtChange.text.toString().fromCurrency().forcePositive()
                                .toString()
                        )
                    refreshPaymentOptions()
                } else {
                    toast("Pilih Bank terlebih dahulu!", Toast.LENGTH_LONG)
                }
            } else {
                toast("Please fill card number")
            }
        }

        vDialogCard.txtAddBank.setOnClickListener {
            this.launchUrl(BuildConfig.WEB_URL + "outlets/bankaccount")
        }
    }

    private fun countChange() {
        var pay = 0
        viewModel.paymentValue.forEach { pay += it.value }
        binding.txtChange.text =
            (pay - binding.txtGrandTotal.text.toString().fromCurrency()).toCurrency()
    }

    private fun saveSales() {
        //validation process
        if (binding.txtChange.text.toString().fromCurrency() < 0) {
            showMessage("Pembayaran yang anda masukan belum mencukupi!")
            return
        }

        Timber.i(">>> PAYMENT FLOW : ${intent.getStringExtra(INTENT_PAY_FLOW)}")
        Timber.i("Last Input Pay : ${binding.edtPay.value()}")

        val receiptReceiver = binding.edtReceiptAddress.text.toString().trim()
        viewModel.savePayment(
            binding.edtPayInfo.value(), binding.edtDueDate.value(),
            binding.txtGrandTotal.text.toString().fromCurrency(),
            receiptReceiver, employee, shift, outlet
        )

        Firebase.analytics
            .logEvent("payment_flow", bundleOf("Flow" to "$paymentFlow - Payment"))

        if (receiptReceiver.isNotEmpty()) {
            val eReceiptType = if (receiptReceiver.isNumeric()) "WhatsApp" else "Email"
            Firebase.analytics
                .logEvent(
                    "e_receipt",
                    bundleOf(
                        "media" to eReceiptType,
                        "receiver" to receiptReceiver
                    )
                )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == RC_SPLITBILL) {
            data?.extras?.getParcelable<SalesEntity?>("sales")?.let { sales ->
                if (sales is SalesEntity) {
                    refreshPaymentOptions()
                    isAfterSplit = true
                    viewModel.sales = sales
                    binding.txtGrandTotal.text = sales.grandTotal.toCurrency()
                    if (sales.orderList?.isEmpty() == true) {
                        showMessage(
                            getString(R.string.all_item_splited_already),
                            onDismissListener = DialogInterface.OnDismissListener {
                                setResult(Activity.RESULT_OK)
                                finish()
                            })
                    } else
                        Snackbar.make(
                            binding.btnSplit,
                            getString(R.string.split_bill_success),
                            Snackbar.LENGTH_LONG
                        ).show()
                }
            }
            refreshPaymentOptions()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_payment, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem) = when (item?.itemId) {
        R.id.action_finish -> {
            currentFocus?.hideKeyboard(this)
            if (isPiutang) viewModel.savePiutang(employee()?.employeeId)
            else saveSales()
            true
        }
        R.id.action_print_bill -> {
            Timber.i(">> Print nota tagihan")
            if (!isNoteListEnable) {
                showMessage("Fitur Note List (pending bill) tidak diaktifkan di outlet ini")
            } else if (paymentFlow != "Cart") {
                showMessage("Hanya transaksi yang telah disimpan yang dapat di print!")
            } else {
                viewModel.printBill(employee, outlet)
            }
            true
        }
        else -> {
//            finish()
            onBackPressed()
            super.onOptionsItemSelected(item)
        }
    }

    override fun onResume() {
        super.onResume()
        initClipboard()
    }

    override fun onBackPressed() {
        Bugsnag.leaveBreadcrumb("onBackPressed-PaymentActivity")
        if (isAfterSplit) {
            setResult(Activity.RESULT_OK)
            super.onBackPressed()
        } else {
            showMessage(
                getString(R.string.warning_bill_not_paid),
                getString(R.string.confirmation),
                { _, _ ->
                    super.onBackPressed()
                })
        }
    }

}