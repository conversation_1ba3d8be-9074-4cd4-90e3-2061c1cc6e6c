package com.uniq.uniqpos.view.ordersales

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.databinding.FragmentOrderDetailsDialogBinding
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import timber.log.Timber

class OrderDetailsDialogFragment : Fragment() {

    private var orderSales: OrderSalesEntity? = null
    private var _binding: FragmentOrderDetailsDialogBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        _binding = FragmentOrderDetailsDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        arguments?.apply {
            val orderSalesJson = getString("orderSalesJson")
            orderSales = Gson().fromJson(orderSalesJson, OrderSalesEntity::class.java)
        }

        orderSales?.let { order ->
            binding.txtOrderId.text = order.orderSalesId
            binding.txtCustomerName.text = order.customer
            binding.txtOrderType.text = order.orderType
            binding.txtOrderStatus.text = order.status
            binding.txtOrderDate.text = order.timeOrder.toString()
            binding.txtOrderTotal.text = order.grandTotal.toString()
            binding.txtOrderItems.text = order.itemNames
        }

        binding.btnClose.setOnClickListener {
            dismiss()
        }
    }

    fun dismiss() {
        parentFragmentManager.beginTransaction().remove(this).commit()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
