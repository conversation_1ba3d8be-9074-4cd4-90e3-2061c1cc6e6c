package com.uniq.uniqpos.view.transaction


import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.databinding.FragmentChooseMenuBinding
import com.uniq.uniqpos.model.LinkMenuProduct
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.ordersales.OrderSalesActivity
import com.uniq.uniqpos.view.ordersummary.OrderSummaryActivity
import com.uniq.uniqpos.view.pendingprint.PendingPrintActivity
import com.uniq.uniqpos.view.scanbarcode.ScanBarcodeActivity
import com.uniq.uniqpos.view.transaction.dialog.ChooseVariantDialog
import com.uniq.uniqpos.view.transaction.dialog.LinkMenuDialog
import com.uniq.uniqpos.view.transaction.promotion.PromotionListFragment
import timber.log.Timber
import kotlin.math.abs

class ChooseMenuFragment : Fragment() {

    private val RC_PAYMENT = 10
    private val RC_CART = 11
    private val RC_SCAN = 12

    private var binding: FragmentChooseMenuBinding? = null
    private lateinit var viewModel: TransactionViewModel
    private lateinit var adapter: ViewPagerAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChooseMenuBinding.inflate(inflater, container, false)
        setHasOptionsMenu(true)
        return binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (activity as MainActivity).getTransactionMain()?.let { transactionMainFragment ->
            viewModel = transactionMainFragment.viewModel

            observeData()
            observeSearchProduct()
            refreshCartBottom()
        }

        adapter = ViewPagerAdapter(childFragmentManager)
        binding?.viewPager?.adapter = adapter
        binding?.tablayout?.post { binding?.tablayout?.setupWithViewPager(binding?.viewPager) }

        binding?.incLayoutCart?.root?.setOnClickListener {
            navigateToResumeOrder()
        }

        binding?.txtPendingOrder?.setOnClickListener {
            startActivity(
                Intent(
                    context,
                    OrderSalesActivity::class.java
                )
            )
        }

        listOf(binding?.fabScan, binding?.incLayoutCart?.btnScan).forEach {
            it?.setOnClickListener {
                val intent = Intent(context, ScanBarcodeActivity::class.java)
                val productWithBarcode = viewModel.products.filter { p -> p.barcode != null }
                if (productWithBarcode.isNotEmpty()) {
                    intent.putExtra("product", (productWithBarcode as ArrayList<ProductEntity>))
                    startActivityForResult(intent, RC_SCAN)
                } else {
                    context?.showMessage(getString(R.string.no_product_barcode))
                }
            }
        }

        binding?.txtError?.setOnClickListener {
            startActivity(
                Intent(
                    context,
                    PendingPrintActivity::class.java
                )
            )
        }
    }

    private fun observeData() {
        viewModel.refreshOrder.observe(viewLifecycleOwner) {
            refreshCartBottom()
        }

        viewModel.refreshFabScan.observe(viewLifecycleOwner) {
            setFabScanVisibility(it ?: true)
        }

        viewModel.refreshProduct.observe(viewLifecycleOwner) {
            if (viewModel.getCategoryList().isNotEmpty()) {
//                initTabCategory()
                getSelectedFragment()?.refreshMenuList()
            }
        }

        viewModel.refreshCategory.observe(viewLifecycleOwner) {
            if (viewModel.products.isNotEmpty()) {
                initTabCategory()
            }
        }

        viewModel.getPendingPrintCount()
            .observe(viewLifecycleOwner) { count ->
                binding?.txtError?.setVisible(count != 0)
                if (count != 0) {
                    binding?.txtError?.text =
                        String.format(getString(R.string.pending_print_count), count)
                    Timber.i("PENDING PRINT SIZE : $count")
                }
            }

        viewModel.barcodeScan.observe(viewLifecycleOwner) { barcode ->
            viewModel.products.firstOrNull { it.barcode == barcode }?.let { product ->
                getSelectedFragment()?.apply {
                    checkStock(product) {
                        addBill(product)
                        Toast.makeText(
                            context,
                            "'${product.name?.toUpperCase()}' added to bill",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            } ?: kotlin.run { Timber.i("'$barcode' Not Found!") }
        }

        viewModel.taskReceiveCart.observe(viewLifecycleOwner) {
            getSelectedFragment()?.refreshMenuList()
            navigateToResumeOrder()
        }

        viewModel.getPendingOrderCountLive().observe(viewLifecycleOwner) {
            Timber.i(">>> count : $it")
            if (it.safe() > 0) {
                binding?.txtPendingOrder?.text = getString(R.string.pending_order_count, it)
                binding?.txtPendingOrder?.setVisible(true)
                binding?.imgPendingOrder?.setVisible(true)
            } else {
                binding?.txtPendingOrder?.setVisible(false)
                binding?.imgPendingOrder?.setVisible(false)
            }
        }

        viewModel.taskShowTutorialProduct.observe(viewLifecycleOwner) {
            binding?.layoutTutorialProduct?.root?.setVisible(it)
        }

        viewModel.taskAddBillNoVariant.observe(viewLifecycleOwner) { data ->
            checkStock(data.first) {
                addBill(data.first, qty = data.second, isNeedToCheckVariant = false)
            }
        }

        viewModel.taskFoundSelfOrder.observe(viewLifecycleOwner) { orders ->
            if (orders.isNullOrEmpty()) {
                (activity as MainActivity).getTransactionMain()
                    ?.updateSelfOrderDialogUi("item tidak ditemukan!")
            } else {
                (activity as MainActivity).getTransactionMain()?.updateSelfOrderDialogUi()
//                viewModel.resetTransaction()
                getSelectedFragment()?.apply {
                    orders.forEach { order ->
//                        addBill(order.product!!, isNeedToCheckVariant = false, isNeedToCheckLinkMenu = false, isHasLinkMenu = false, qty = order.qty)
                    }
                    navigateToResumeOrder()
                }
//                it.forEach { order -> addToBill(order.product!!, isNeedToCheckLinkMenu = false, isNeedToCheckVariant = false, qty = order.qty) }
            }
        }
    }

    private fun refreshCartBottom() {
        val qty = viewModel.orders.filter { !it.isItemVoid }.sumBy { abs(it.qty) }
        binding?.incLayoutCart?.txtQtyOrder?.text =
            resources.getQuantityString(R.plurals.item, qty, qty)
        binding?.incLayoutCart?.txtGrandtotal?.text =
            "Rp${viewModel.calculateGrandTotal().toCurrency()}"
        if (viewModel.orders.isEmpty()) {
            binding?.incLayoutCart?.root?.visibility = View.GONE
        } else if (binding?.incLayoutCart?.root?.visibility == View.GONE) {
            binding?.incLayoutCart?.root?.visibility = View.VISIBLE
        }
        setFabScanVisibility(viewModel.orders.isEmpty())
    }

    private fun navigateToResumeOrder() {
        val intent = Intent(context, OrderSummaryActivity::class.java)
        intent.putExtra("order", viewModel.orders)
        intent.putExtra("tax_sales", ArrayList(viewModel.taxSaleList.filter { it.total > 0 }))
        intent.putExtra("sales", viewModel.salesEdit)
        intent.putExtra("discount", viewModel.discount)
        intent.putExtra("merge_ids", viewModel.mergeIds)
        startActivityForResult(intent, RC_PAYMENT)
    }

    private fun observeSearchProduct() {
        viewModel.searchProduct.observe(viewLifecycleOwner, Observer {
            try {
                getSelectedFragment()?.search(it)
            } catch (e: Exception) {
                Timber.i(">>> SEARCH MENU ERROR : $e")
            }
        })
    }

    private fun initTabCategory() {
//        vi.view_pager.removeAllViews()
        binding?.tablayout?.removeAllTabs()
        adapter.clearFragments()
        viewModel.getCategoryList().forEach { category ->
            if(category.productCategoryId == Constant.MenuCategoryPromo){
                val fragment = PromotionListFragment.newInstance(viewModel.promotionList)
                adapter.addFragment(fragment, category.name ?: "")
            }else{
                val fragment = MenuFragment.newInstance(category.productCategoryId)
                adapter.addFragment(fragment, category.name ?: "")
            }
        }
        adapter.notifyDataSetChanged()
        if(adapter.getItem(0) is PromotionListFragment){
            binding?.viewPager?.currentItem = 1
        }
    }

    fun checkStock(product: ProductEntity, listener: () -> Unit) {
        if (!viewModel.isHasVariant(product.productId) && (product.stock == Constant.STOCK_UNAVAILABLE || (product.stockQty == 0 && product.stockManagement == 1))) {
            context?.showMessage(
                getString(R.string.question_adding_no_stock_product),
                getString(R.string.run_out),
                { _, _ ->
                    listener()
                })
        } else {
            listener()
        }
    }

    fun addBill(
        product: ProductEntity,
        isNeedToCheckVariant: Boolean = true,
        isNeedToCheckLinkMenu: Boolean = true,
        isHasLinkMenu: Boolean = false,
        qty: Int = 1
    ) {
//        Timber.d("this will add to bill : ${Gson().toJson(viewModel.products.filter { it.productId == product.productId })}")
        if (isNeedToCheckVariant) {
            if (viewModel.isHasVariant(product.productId)) {
                object : ChooseVariantDialog(requireContext(), viewModel, product.productId) {
                    override fun onItemSelected(productEntity: ProductEntity) {
                        addBill(productEntity, isNeedToCheckVariant = false)
                    }
                }.show()
                return
            }
        }

        if (isNeedToCheckLinkMenu) {
            val linkMenu =
                viewModel.linkMenuList.filter { it.productDetailFkid == product.productDetailId }
            if (linkMenu.isNotEmpty()) {
                object : LinkMenuDialog(requireContext(), viewModel) {
                    override fun addToBill(
                        productEntity: ProductEntity,
                        linkMenuSelected: List<LinkMenuProduct>,
                        qty: Int
                    ) {
                        addBill(
                            productEntity,
                            isNeedToCheckVariant = false,
                            isNeedToCheckLinkMenu = false,
                            isHasLinkMenu = true,
                            qty = qty
                        )
                        linkMenuSelected.forEach {
                            addExtra(
                                it.product.copy(priceSell = it.linkMenuDetail.priceAdd),
                                "link"
                            )
                        }
                    }
                }.showLinkMenu(product)
                return
            }
        }

        viewModel.orders.takeIf { !isHasLinkMenu }
            ?.firstOrNull { it.product?.productDetailId == product.productDetailId }?.let {
                it.qty = it.qty + 1
                viewModel.updatePrice(it)
                viewModel.updateTax(it, viewModel.calculateDiscPerItem(it, it))
                viewModel.refreshOrder.call()
            } ?: kotlin.run {
            viewModel.orders.add(Order(product, qty))
            viewModel.updatePrice(viewModel.orders.last())
            viewModel.updateTax(viewModel.orders.last())
            viewModel.refreshOrder.call()
        }

        getSelectedFragment()?.apply {
            refreshMenuList()
        }
    }

    //tag : can be extra or link
    private fun addExtra(productEntity: ProductEntity, tag: String = "extra") {
        Timber.i(">> Add Extra : ${productEntity.name}")
        val order = Order(
            productEntity, extra = ArrayList(), employeeId = context?.employee()?.employeeId
                ?: 0, qty = 1, extraType = tag
        )
//        val disc = viewModel.calculateDiscPerItem(order, viewModel.orders[viewModel.orders.size - 1])
        viewModel.orders.lastOrNull()?.let { orderParent ->
            viewModel.updatePrice(order)
            viewModel.updateTax(order, viewModel.calculateDiscPerItem(order, orderParent))
            viewModel.orders.lastOrNull()?.extra?.add(order)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == RC_PAYMENT) {
                val fromActivity = data?.getStringExtra("from")
                if (fromActivity == OrderSummaryActivity::class.simpleName) {
                    val sales = data?.extras?.getParcelable<SalesEntity>("sales")
                    sales?.let { viewModel.initSalesData(sales, isFromCart = false) }
                    getSelectedFragment()?.refreshMenuList()
                } else {
                    viewModel.resetTransaction()
                    viewModel.refreshOrder.call()
                    getSelectedFragment()?.refreshMenuList()

                    val bundle = data?.extras
                    val dataSales = bundle?.getParcelable<SalesEntity>("sales")
                    if (dataSales is SalesEntity) {
                        Timber.i("order list (save) : ${Gson().toJson(dataSales)}")
                        viewModel.syncSales(dataSales)
                        val isPayment = (bundle.getSerializable("isPayment") as? Boolean) ?: false
                        (activity as MainActivity).getTransactionMain()?.apply {
                            printSales(dataSales, !isPayment)
                            if (!isPayment) sendSalesCartToLocalServer(
                                TmpSalesEntity(
                                    dataSales.noNota,
                                    Gson().toJson(dataSales),
                                    outlet.outletId.safe()
                                )
                            )
                        }
                    }
                }
            } else if (requestCode == RC_SCAN) {
                data?.extras?.getParcelableArrayList<Order>("order")?.let { orderList ->
                    viewModel.orders.addAll(orderList)
                    viewModel.orders.forEach { viewModel.updatePrice(it) }
                    viewModel.reCalculateTax()
                    viewModel.refreshOrder.call()
                    getSelectedFragment()?.refreshMenuList()
                } ?: kotlin.run { context?.toast("No Data!") }
            }
        }
    }

    private fun setFabScanVisibility(isVisible: Boolean) {
        if (isVisible && binding?.fabScan?.isShown == false && binding?.incLayoutCart?.root?.visibility == View.GONE) binding?.fabScan?.show()
        else if (!isVisible && binding?.fabScan?.isShown == true) binding?.fabScan?.hide()
    }

    private fun getSelectedFragment(): MenuFragment? {
        val tag =
            "android:switcher:" + binding?.viewPager?.id + ":" + binding?.tablayout?.selectedTabPosition
        val selectedFragment = childFragmentManager.findFragmentByTag(tag)
        Timber.d("[menu] selectedFragment: $selectedFragment")
        return (selectedFragment as? MenuFragment)
    }
}
