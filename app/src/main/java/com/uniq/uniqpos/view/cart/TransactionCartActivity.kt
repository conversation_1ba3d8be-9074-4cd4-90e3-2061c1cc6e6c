package com.uniq.uniqpos.view.cart

import android.app.Activity
import android.app.AlertDialog
import android.app.SearchManager
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.Gravity
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.SearchView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.simplify
import com.uniq.uniqpos.databinding.*
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialog
import com.uniq.uniqpos.util.lifecycle.setupToast
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.payment.PaymentActivity
import com.uniq.uniqpos.view.payment.PaymentV2Activity
import smartdevelop.ir.eram.showcaseviewlib.GuideView
import smartdevelop.ir.eram.showcaseviewlib.config.DismissType
import timber.log.Timber

class TransactionCartActivity : BaseActivity<TransactionCartViewModel, ActivityTransactionListBinding>() {

    private val printers = ArrayList<PrinterEntity>()

    private lateinit var searchView: SearchView
    private var searchListener: SearchView.OnQueryTextListener? = null
    private var isSearchLogged = false
    private lateinit var cartListAdapter: GlobalAdapter<ItemTransactionListBinding>
    private lateinit var cartTableAdapter: GlobalAdapter<ListItemCartTableBinding>
    private val listCartTable = ArrayList<SalesEntity>()

    override fun getLayoutRes() = R.layout.activity_transaction_list
    override fun getViewModel() = TransactionCartViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (resources.getBoolean(R.bool.landscape_only)) requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        Timber.i("Open Transaction Cart / NoteList")
    }

    override fun initView() {
        super.initView()
        val role = role()

        cartListAdapter = object : GlobalAdapter<ItemTransactionListBinding>(R.layout.item_transaction_list, viewModel.saleList, binding.incLayNoData.root) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ItemTransactionListBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                if (!role.pembayaran) holder.binding.btnPay.setVisible(false)
                holder.binding.btnPay.setOnClickListener { viewModel.saleList.getSafe(holder.adapterPosition)?.let { payBill(it) } }
                holder.binding.btnEdit.setOnClickListener { viewModel.saleList.getSafe(holder.adapterPosition)?.let { editSale(it) } }
                holder.binding.btnPrint.setOnClickListener { viewModel.saleList.getSafe(holder.adapterPosition)?.let { selectPrinter(it) } }
                holder.binding.txtItems.setOnClickListener { viewModel.saleList.getSafe(holder.adapterPosition)?.let { showOrderDetail(it) } }
                holder.binding.btnMerge.apply {
                    setOnClickListener { viewModel.saleList.getSafe(holder.adapterPosition)?.let { showMergeBillDialog(it) } }
                    if (position == 0) {
                        showCaseMergeBill(this)
                    }
                }
            }
        }

        cartTableAdapter = object : GlobalAdapter<ListItemCartTableBinding>(R.layout.list_item_cart_table, listCartTable) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemCartTableBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                holder.binding.root.setOnClickListener { listCartTable.getSafe(holder.adapterPosition)?.let { editSale(it) } }
            }
        }

        changeView("list")

        val outlet = outlet()
        viewModel.loadActiveDevice(outlet?.outletId.safe())
        searchListener = object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(q: String?): Boolean {
                searchTransaction(q)
                return true
            }
        }

        binding.swipeRefresh.setOnRefreshListener { binding.swipeRefresh.isRefreshing = false }
        binding.btnViewTable.setOnClickListener { changeView("table") }
        binding.btnViewList.setOnClickListener { changeView("list") }

        //init feature
        outletFeature().apply {
            binding.groupViewSetting.setVisible(tableinput)
        }
    }

    private fun changeView(view: String) {
        if (view == "table") {
            changeSwitchUI(binding.btnViewList, binding.btnViewTable, Gravity.RIGHT, this)
            listCartTable.clear()
            listCartTable.addAll(viewModel.saleList.filter { it.table.isNotBlank() })
            listCartTable.sortWith(compareBy { if(it.table.isNumeric()) it.table.toInt() else it.table.length + 100 })

            binding.recview.layoutManager = GridLayoutManager(this, resources.getInteger(R.integer.table_span_count))
            binding.recview.adapter = cartTableAdapter
            binding.recview.adapter?.notifyDataSetChanged()
        } else {
            changeSwitchUI(binding.btnViewList, binding.btnViewTable, Gravity.LEFT, this)
            binding.recview.layoutManager = LinearLayoutManager(this)
            binding.recview.adapter = cartListAdapter
        }
    }

    private fun searchTransaction(query: String?) {
        query?.takeIf { it.isNotEmpty() }?.let { query ->
            viewModel.saleList.clear()
            viewModel.saleListTmp.filter { it.customer.safe().lowercase().contains(query.lowercase()) || it.items.safe().lowercase().contains(query.lowercase()) }.forEach { viewModel.saleList.add(it) }
            logSearch()
        } ?: kotlin.run {
            viewModel.saleList.clear()
            viewModel.saleList.addAll(viewModel.saleListTmp)
        }
        binding.recview.adapter?.notifyDataSetChanged()
    }

    private fun logSearch() {
        if (!isSearchLogged) {
            isSearchLogged = true
            val outlet = outlet()
            Firebase.analytics
                    .logEvent("app_features",
                            bundleOf("Feature" to "Search Menu Cart",
                                    "Outlet" to "SMC:${outlet?.outletId}:${outlet?.name}"))
        }
    }

    override fun observeData() {
        viewModel.printers.observe(this, { printerList ->
            printers.clear()
            printerList?.filter { p -> !deactivatePrinterList().any { it == p.address } }?.let { printerFiltered ->
                printers.addAll(printerFiltered)
            }
        })

        val outlet = outlet()
        viewModel.getTransactionCartLive(outlet?.outletId)
                .observe(this, { items ->
                    viewModel.convertSalesJson(items.map { it.sales })
                })
    }

    override fun observeTask() {
        setupToast(this, viewModel.toastMsg)
        setupDialogMessage(this, viewModel.dialogMsg)
        setupLoadingDialog(this, this, viewModel.loadingDialog)

        binding.swipeRefresh.isRefreshing = true
        viewModel.taskRefreshSales.observe(this, { event ->
            event.getContentIfNotHandled()?.let {
                binding.recview.post {
                    binding.recview.adapter?.notifyDataSetChanged()
                    binding.swipeRefresh.isRefreshing = false
                }
            }
        })

        viewModel.taskContinueEdit.observe(this, { event ->
            event.getContentIfNotHandled()?.let { sales ->
                navigateToEditSaleCart(sales)
            }
        })

        viewModel.taskContinueEditWithMerge.observe(this, { event ->
            event.getContentIfNotHandled()?.let { bundle ->
                val intent = Intent()
                intent.putExtras(bundle)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        })
    }

    private fun showCaseMergeBill(view: View) {
        val employee = employee()
        val key = "merge_bill-${employee?.employeeId}"
        if (!getLocalDataBoolean(key)) {
            binding.recview.post {
                GuideView.Builder(this)
                        .setTitle("Merge Bill")
                        .setContentText("klik Merge untuk menggabungkan satu Bill dengan Bill yang lain")
                        .setTargetView(view)
                        .setTitleTextSize(17)
                        .setDismissType(DismissType.anywhere)
                        .build()
                        .show()
                putData(key, true)
            }
        }
    }

    private fun showMergeBillDialog(salesEntity: SalesEntity) {
        if (viewModel.saleList.size == 1) {
            showAlert(getString(R.string.warn_merge_can_not_be_done))
            return
        }

        val chosenIds = ArrayList<String>()
        val billList = ArrayList<SalesEntity>()
        billList.addAll(viewModel.saleList.filter { it.noNota != salesEntity.noNota })

        val dialog = androidx.appcompat.app.AlertDialog.Builder(this)
                .create()

        val bindingMerge = DataBindingUtil.inflate<DialogMergeBillBinding>(layoutInflater, R.layout.dialog_merge_bill, null, false)
        bindingMerge.model = salesEntity

        bindingMerge.edtSearch.simpleOnTextChanged { q ->
            billList.clear()
            if (q.isEmpty()) {
                billList.addAll(viewModel.saleList.filter { it.noNota != salesEntity.noNota })
            } else {
                billList.addAll(viewModel.saleList.filter { it.noNota != salesEntity.noNota && (it.customer.safe().toLowerCase().contains(q.toLowerCase()) || it.table.toLowerCase().contains(q.toLowerCase())) })
            }
            bindingMerge.recviewBillNames.adapter?.notifyDataSetChanged()
        }
        bindingMerge.txtCancel.setOnClickListener { dialog.dismiss() }
        bindingMerge.txtNext.setOnClickListener {
            if (chosenIds.isEmpty()) {
                toast(getString(R.string.warn_select_bill))
            } else {
                processMergeBills(salesEntity, chosenIds)
                dialog.dismiss()
            }
        }

        bindingMerge.recviewBillNames.adapter = object : GlobalAdapter<ListItemBillNamesBinding>(R.layout.list_item_bill_names, billList) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemBillNamesBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                holder.binding.cbBill.setOnCheckedChangeListener { _, isChecked ->
                    if (isChecked) chosenIds.add(billList[holder.adapterPosition].noNota)
                    else chosenIds.remove(billList[holder.adapterPosition].noNota)
                }
                holder.binding.cbBill.isChecked = chosenIds.any { it == billList[holder.adapterPosition].noNota }
            }
        }

        dialog.setView(bindingMerge.root)
        dialog.show()
    }

    private fun processMergeBills(salesEntity: SalesEntity, chosenIds: java.util.ArrayList<String>) {
        viewModel.mergeBill(salesEntity, chosenIds)
        outlet()?.let { outlet ->
            var deviceID = sharedPref().getString(SharedPref.DEVICE_ID)
            Firebase.analytics
                    .logEvent("merge_bill",
                            bundleOf("Outlet" to "${outlet.outletId}:${outlet.name}",
                                    "Identifier" to "${outlet.outletId}:$deviceID:${System.currentTimeMillis().dateTimeFormat("HH:mm dd-MM")}"))
        }
    }

    private fun selectPrinter(salesEntity: SalesEntity) {
        val printerReceipt = printers//.filter { it.settingPrintreceipt == "1" }
        when {
            printers.isEmpty() -> showMessage(getString(R.string.have_no_printer))
            printerReceipt.size > 1 -> showPrinterOption(salesEntity, printerReceipt)
            printerReceipt.size == 1 -> printBill(salesEntity, printerReceipt[0])
            else -> showPrinterOption(salesEntity, printers)
        }
    }

    private fun showPrinterOption(sales: SalesEntity, printerList: List<PrinterEntity>) {
        val optionList = ArrayList<String?>()
        printerList.forEach { optionList.add(it.name) }
        val options = optionList.toTypedArray()
        AlertDialog.Builder(this)
                .setItems(options) { _, position ->
                    printBill(sales, printerList[position])
                }
                .setTitle("Choose Printer")
                .setNegativeButton(R.string.cancel, null)
                .show()
    }

    private fun showOrderDetail(salesEntity: SalesEntity) {
        val format = StringBuilder()
        salesEntity.orderList?.forEach { order ->
            if (order.isItemVoid) format.appendnl("VOID =>")
            format.append("[" + order.tmpId.dateFormat("HH:mm:ss") + "] ")
            format.append(" ${order.qty}X ".width(5))
            format.append(order.product?.name?.width(30))
            format.appendnl(order.subTotal.toCurrency().width(11))
            order.extra.forEach { extra ->
                format.append("⇨  ${extra.qty}X ".width(5))
                format.append(extra.product?.name?.width(20))
                format.appendnl(extra.subTotal.toCurrency())
            }
            order.note?.let { format.appendnl("NB: $it") }
            order.employeeName?.let { format.appendnl("($it)") }
            format.append("\n")
        }
//        showMessage(format.toString(), "NOTA  ${salesEntity.noNota} ")
        if (salesEntity.orderList == null) return

        val dialog = androidx.appcompat.app.AlertDialog.Builder(this)
                .create()

        val dialogBinding: DialogCartItemBinding = DataBindingUtil.inflate(layoutInflater, R.layout.dialog_cart_item, null, false)

        dialogBinding.recViewItem.adapter = object : GlobalAdapter<ListItemCartDialogBinding>(R.layout.list_item_cart_dialog, salesEntity.orderList) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemCartDialogBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                salesEntity.orderList?.getSafe(position)?.let { order ->
                    val format = StringBuilder()
                    format.append("[" + order.tmpId.dateFormat("HH:mm:ss") + "] ")
                    format.append(" ${order.qty}X ".width(5))
                    format.append(order.product?.name)
                    order.extra.forEach { extra ->
                        format.append("\n ⇨" + extra.product?.name + "   ")
                        format.append(extra.subTotal.toCurrency())
                    }
//                  order.employeeName?.let { format.appendnl("($it)") }
                    holder.binding.txtTitle.text = format
                }
            }
        }

        dialogBinding.txtTitle.text = salesEntity.customer?.uppercase()
        dialogBinding.txtPay.setOnClickListener { payBill(salesEntity) }
        dialogBinding.txtEdit.setOnClickListener { editSale(salesEntity) }
        dialogBinding.txtPrint.setOnClickListener { selectPrinter(salesEntity) }
        dialogBinding.imgPrint.setOnClickListener { selectPrinter(salesEntity) }
        salesEntity.note?.takeIf { it.isNotEmpty() }?.let { note -> dialogBinding.txtNote.text = "Note: $note" }

        dialog.setView(dialogBinding.root)
        dialog.show()
    }

    private fun printBill(sales: SalesEntity, printer: PrinterEntity) {
        showDialog(true, "printing...")

        val outlet = outlet() ?: Outlet()
        val employee = employee() ?: Employee()

        val printDataList = java.util.ArrayList<PendingPrintEntity>()
        val notaFormat = PrintNotaUtil.getPrintNotaFormat(sales, outlet, employee, true, printer.settingPrintpapersize)
        Timber.i("Print Nota Tagihan. ${sales.noNota} - Paper Size : ${printer.settingPrintpapersize}")
        Timber.i("Nota : \n${notaFormat.first}")
        printDataList.add(PendingPrintEntity(printer.address, printer.type, notaFormat.first + "\n\n ", printer.name, System.currentTimeMillis()))

        beginPrint(printDataList) {
            showDialog(false)
        }
    }

    private fun editSale(salesEntity: SalesEntity) {
        Timber.i("EDIT :: ${Gson().toJson(salesEntity)}")
        if (viewModel.availableDevices.isEmpty() || viewModel.availableDevices.size > 1) {
            viewModel.editSalesCart(salesEntity)
        } else {
            navigateToEditSaleCart(salesEntity)
        }
    }

    private fun navigateToEditSaleCart(salesEntity: SalesEntity) {
        val bundle = Bundle()
        bundle.putParcelable("sales", salesEntity)
        val intent = Intent()
        intent.putExtras(bundle)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun payBill(saleList: SalesEntity) {
        if (!role().pembayaran) {
            toast(getString(R.string.no_permission))
            return
        }

        if (saleList.orderList?.isEmpty() == true) {
            showMessage("Bill has empty item. Delete this bill?", "Confirmation", DialogInterface.OnClickListener { _, _ ->
                viewModel.updateTmpSales(TmpSalesEntity(saleList.noNota, "", saleList.outletID, Constant.SALES_STATUS_PAID))
            })
            return
        }

        Firebase.analytics
                .logEvent(FirebaseAnalytics.Event.BEGIN_CHECKOUT,
                        bundleOf(FirebaseAnalytics.Param.VALUE to saleList.grandTotal.toString(),
                                FirebaseAnalytics.Param.CURRENCY to "IDR"))

        val productDetailIds = saleList.orderList?.map { it.product?.productDetailId.safe() } ?: listOf()
        saleList.promotions = saleList.promotions?.simplify(productDetailIds)

        val useNewUi = Firebase.remoteConfig.getBoolean("ui_payment")
        var intent = if(useNewUi) Intent(this, PaymentV2Activity::class.java) else Intent(this, PaymentActivity::class.java)
//        var intent = Intent(this, PaymentActivity::class.java)

        intent.putExtra("sales", saleList)
        intent.putExtra(PaymentActivity.INTENT_PAY_FLOW, "Cart")
        intent.addFlags(Intent.FLAG_ACTIVITY_FORWARD_RESULT)
        startActivity(intent)
//        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_transaction_cart, menu)
        val searchManager = getSystemService(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu?.findItem(R.id.action_search)?.actionView as SearchView
        searchView.setSearchableInfo(searchManager.getSearchableInfo(componentName))
        searchListener?.let { searchView.setOnQueryTextListener(it) }
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
