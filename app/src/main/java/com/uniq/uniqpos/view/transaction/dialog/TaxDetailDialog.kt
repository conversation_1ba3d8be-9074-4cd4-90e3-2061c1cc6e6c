package com.uniq.uniqpos.view.transaction.dialog

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.SparseArray
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.GratuityEntity
import com.uniq.uniqpos.databinding.DialogTaxDetailBinding
import com.uniq.uniqpos.databinding.ListItemTaxesBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.view.DialogAuthorization
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber

/**
 * Created by annasblackhat on 31/07/18
 */
abstract class TaxDetailDialog(
    mContext: Context,
    val viewModel: TransactionViewModel
) : AlertDialog(mContext) {

    private lateinit var binding: DialogTaxDetailBinding
    private val tmpTaxesEnable = SparseArray<Boolean>()
    private val gratuityList = ArrayList<GratuityEntity>()

    private var discountType = Constant.TYPE_PERSEN
    private var voucherType = Constant.TYPE_PERSEN

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        requestWindowFeature(Window.FEATURE_NO_TITLE)
        binding = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.dialog_tax_detail,
            null,
            false
        )
        setContentView(binding.root)
//        if (context.resources.getBoolean(R.bool.landscape_only)) {
//            window.setLayout(WindowManager.LayoutParams.WRAP_CONTENT,
//                    WindowManager.LayoutParams.WRAP_CONTENT)
//        } else {
//            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT,
//                    WindowManager.LayoutParams.WRAP_CONTENT)
//        }

        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        initView()
    }

    private fun initView() {
        val permissionModifyTax = context.role().gantipajak

        val model = viewModel.gratuities.sortedBy { it.name }
        Timber.i("tax size : ${model.size}")
        gratuityList.clear()
        gratuityList.addAll(model.take(5))

        binding.viewShowMoreTax.setVisible(model.size > 5)
        binding.viewShowMoreTax.setOnClickListener {
            gratuityList.clear()
            gratuityList.addAll(viewModel.gratuities.sortedBy { it.name })
            binding.recViewTaxes.adapter?.notifyDataSetChanged()
            binding.viewShowMoreTax.setVisible(false)
        }

        binding.recViewTaxes.adapter =
            object : GlobalAdapter<ListItemTaxesBinding>(R.layout.list_item_taxes, gratuityList) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemTaxesBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
                    gratuityList.getSafe(holder.adapterPosition)?.let { gratuity ->
                        Timber.i("tax :: ${gratuity.name} (${gratuity.gratuityId}) --> ${viewModel.taxesEnable[gratuity.gratuityId]} | status : ${gratuity.taxStatus}")
                        val isEnableToChange =
                            if (gratuity.taxStatus == "permanent") false else permissionModifyTax
                        val taxEnable = viewModel.taxesEnable[gratuity.gratuityId] ?: false
                        holder.binding.cbTax.isChecked =
                            gratuity.taxStatus == "permanent" || taxEnable

                        //holder.itemView.cb_tax.isEnabled = isEnableToChange
                        holder.binding.cbTax.setOnClickListener {
                            Timber.i("[TAX DIALOG] user clicking on ${gratuity.name} | isChecked ? ${holder.binding.cbTax.isChecked}")
                            if (isEnableToChange) {
                                tmpTaxesEnable.put(
                                    gratuity.gratuityId,
                                    holder.binding.cbTax.isChecked
                                )
                            } else {
                                context.toast(
                                    if (!permissionModifyTax) R.string.no_permission else R.string.tax_permanent_info,
                                    level = Level.ERROR
                                )
                                holder.binding.cbTax.isChecked =
                                    gratuity.taxStatus == "permanent" || taxEnable
                            }
                        }
                    }
                }
            }

        var isAuthorized = false
        if (!context.role().gantidiskonperbill) {
            binding.dialogDiscount.isFocusable = false

            binding.dialogDiscount.setOnClickListener {
                if (!isAuthorized) {
                    DialogAuthorization(context)
                        .setAuthType(DialogAuthorization.AuthType.DISCOUNT_BILL)
                        .setOnAuthorizedListener {
                            binding.dialogDiscount.isFocusableInTouchMode = true
                            binding.dialogDiscount.requestFocus()
                            isAuthorized = true
                        }
                        .show()
                }
            }
        }

        if (viewModel.discount.discountType == Constant.TYPE_PERSEN) {
            changeSwitchUI(binding.btnDiscNominal, binding.btnDiscPercent, Gravity.RIGHT, context)
            discountType = Constant.TYPE_PERSEN
        } else if (viewModel.discount.discountType == Constant.TYPE_NOMINAL) {
            changeSwitchUI(binding.btnDiscNominal, binding.btnDiscPercent, Gravity.LEFT, context)
            discountType = Constant.TYPE_NOMINAL
        }

        if (viewModel.discount.voucherType == Constant.TYPE_PERSEN) {
            changeSwitchUI(
                binding.btnVoucherNominal,
                binding.btnVoucherPercent,
                Gravity.RIGHT,
                context
            )
            voucherType = Constant.TYPE_PERSEN
        } else if (viewModel.discount.voucherType == Constant.TYPE_NOMINAL) {
            changeSwitchUI(
                binding.btnVoucherNominal,
                binding.btnVoucherPercent,
                Gravity.LEFT,
                context
            )
            voucherType = Constant.TYPE_NOMINAL
        }

        binding.dialogDiscount.liveToCurrencyAndWatch { total ->
            if (total.fromCurrency() > 100 && discountType == Constant.TYPE_PERSEN) {
                changeSwitchUI(
                    binding.btnDiscNominal,
                    binding.btnDiscPercent,
                    Gravity.LEFT,
                    context
                )
                discountType = Constant.TYPE_NOMINAL
            } else if (total.fromCurrency() <= 100 && discountType == Constant.TYPE_NOMINAL) {
                changeSwitchUI(
                    binding.btnDiscNominal,
                    binding.btnDiscPercent,
                    Gravity.RIGHT,
                    context
                )
                discountType = Constant.TYPE_PERSEN
            }
        }

        binding.edtVoucher.liveToCurrencyAndWatch { total ->
            if (total.fromCurrency() > 100 && voucherType == Constant.TYPE_PERSEN) {
                voucherType = Constant.TYPE_NOMINAL
                changeSwitchUI(
                    binding.btnVoucherNominal,
                    binding.btnVoucherPercent,
                    Gravity.LEFT,
                    context
                )
            } else if (total.fromCurrency() <= 100 && voucherType == Constant.TYPE_NOMINAL) {
                voucherType = Constant.TYPE_PERSEN
                changeSwitchUI(
                    binding.btnVoucherNominal,
                    binding.btnVoucherPercent,
                    Gravity.RIGHT,
                    context
                )
            }
        }

        binding.btnSubmit.setOnButtonClickListener {
            Timber.i("Submit... (Tax Detail Dialog)")
            val dsc = binding.dialogDiscount.text.toString().fromCurrency()
            val voucher = binding.edtVoucher.text.toString().fromCurrency()

            var isValid = true
            if (dsc > 0 && binding.dialogDiscountInfo.text.toString().isBlank()) {
                isValid = false
                context.showMessage(context.getString(R.string.disc_can_not_empty))
            } else {
                viewModel.discount.discount = dsc
                viewModel.discount.discountType = discountType
                if (dsc == 0) binding.dialogDiscountInfo.setText("")
                viewModel.discount.discountInfo = binding.dialogDiscountInfo.text.toString().trim()
            }

            if (voucher > 0 && binding.edtVoucherInfo.text.toString().isBlank()) {
                isValid = false
                context.showMessage("Isi Keterangan Voucher!")
            } else {
                viewModel.discount.voucher = voucher
                viewModel.discount.voucherType = voucherType
                if (voucher == 0) binding.edtVoucherInfo.setText("")
                viewModel.discount.voucherInfo = binding.edtVoucherInfo.text.toString().trim()
            }

            if (viewModel.discount.discountType == Constant.TYPE_PERSEN && dsc > 100) {
                isValid = false
                context.showMessage(context.getString(R.string.disc_exceed_limit))
            }

            if (viewModel.discount.voucherType == Constant.TYPE_PERSEN && voucher > 100) {
                isValid = false
                context.showMessage(context.getString(R.string.voucher_exceed_limit))
            }

            if (dsc > 0 && viewModel.orders.isNotEmpty()) {
                val totalItemActiveDisc =
                    viewModel.orders.filter { it.product?.discount == "on" }.size
                if (totalItemActiveDisc == 0) {
                    isValid = false
                    context.showMessage(
                        context.getString(R.string.no_item_active_disc), "DISCOUNT DISABLE",
                        negativeAction = { _, _ ->
                            context.launchUrl("${BuildConfig.WEB_URL}products/catalogue")
                        },
                        negativeMsg = "OPEN PRODUCT CATALOGUE"
                    )
                } else if (totalItemActiveDisc < viewModel.orders.size) {
                    context.toast(
                        "only some of your item menu activated for discount",
                        level = Level.WARNING
                    )
                }
            }

            if (voucher > 0 && viewModel.orders.isNotEmpty()) {
                val totalItemActiveVoucher =
                    viewModel.orders.filter { it.product?.voucher == "on" }.size
                if (totalItemActiveVoucher == 0) {
                    isValid = false
                    context.showMessage(
                        context.getString(R.string.no_item_active_disc),
                        "VOUCHER DISABLED",
                        negativeAction = { _, _ ->
                            context.launchUrl("${BuildConfig.WEB_URL}products/catalogue")
                        },
                        negativeMsg = "OPEN PRODUCT CATALOGUE"
                    )
                } else if (totalItemActiveVoucher < viewModel.orders.size) {
                    context.toast(
                        "only some of your item menu activated for voucher",
                        level = Level.WARNING
                    )
                }
            }

            if (isValid) {
                for (i in 0 until tmpTaxesEnable.size()) {
                    val key = tmpTaxesEnable.keyAt(i)
                    viewModel.taxesEnable.put(key, tmpTaxesEnable[key])
                    val gratuity = viewModel.gratuities.first { it.gratuityId == key }
                    Timber.i("tax : ${gratuity.name} ($key) --> ${tmpTaxesEnable[key]}")
                    if (tmpTaxesEnable[key]) {
                        context.toast(
                            "kamu mengaktifkan '${gratuity.name?.uppercase()}'",
                            Toast.LENGTH_LONG
                        )
                    }
                }

                Timber.i("discount : ${Gson().toJson(viewModel.discount)}")
                viewModel.reCalculateTax()
                viewModel.clearZeroTax()
                viewModel.saveTaxAndGratuity()
                isAuthorized = context.role().gantidiskonperbill
                dismiss()
                onTaxSaved()
            }
        }

        binding.btnCancel.setOnButtonClickListener { dismiss() }
        binding.btnDiscNominal.setOnClickListener {
            discountType = Constant.TYPE_NOMINAL; changeSwitchUI(
            binding.btnDiscNominal,
            binding.btnDiscPercent,
            Gravity.LEFT,
            context
        )
        }
        binding.btnDiscPercent.setOnClickListener {
            discountType = Constant.TYPE_PERSEN; changeSwitchUI(
            binding.btnDiscNominal,
            binding.btnDiscPercent,
            Gravity.RIGHT,
            context
        )
        }
        binding.btnVoucherNominal.setOnClickListener {
            voucherType = Constant.TYPE_NOMINAL; changeSwitchUI(
            binding.btnVoucherNominal,
            binding.btnVoucherPercent,
            Gravity.LEFT,
            context
        )
        }
        binding.btnVoucherPercent.setOnClickListener {
            voucherType = Constant.TYPE_PERSEN; changeSwitchUI(
            binding.btnVoucherNominal,
            binding.btnVoucherPercent,
            Gravity.RIGHT,
            context
        )
        }

        binding.txtAddTax.setOnClickListener {
            context.launchUrl("${BuildConfig.WEB_URL}products/taxgratuity")
        }
    }

    override fun show() {
        super.show()
        binding.recViewTaxes.adapter?.notifyDataSetChanged()
        binding.dialogDiscount.setText(viewModel.discount.discount.toString())

        binding.dialogDiscountInfo.setText(viewModel.discount.discountInfo)
        binding.edtVoucher.setText(viewModel.discount.voucher.toString())
        binding.edtVoucherInfo.setText(viewModel.discount.voucherInfo)

        val adapter = ArrayAdapter(
            context,
            android.R.layout.simple_dropdown_item_1line,
            viewModel.discAndVoucherInfoHistory
        )
        binding.edtVoucherInfo.setAdapter(adapter)
        binding.dialogDiscountInfo.setAdapter(adapter)
    }

    override fun dismiss() {
        tmpTaxesEnable.clear()
        currentFocus?.hideKeyboard(context)
        super.dismiss()
    }

    abstract fun onTaxSaved()
    abstract fun startActivityWithResult(intent: Intent, requestCode: Int)
}