package com.uniq.uniqpos.view.transaction

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.FragmentMenuBinding
import com.uniq.uniqpos.databinding.ListItemChooseMenuBinding
import com.uniq.uniqpos.model.LinkMenuProduct
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.employee
import com.uniq.uniqpos.util.outletFeature
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.transaction.dialog.ChooseVariantDialog
import com.uniq.uniqpos.view.transaction.dialog.LinkMenuDialog
import com.uniq.uniqpos.view.transaction.utils.searchProductByKeyword
import timber.log.Timber
import kotlin.math.abs

class MenuFragment : Fragment() {

    companion object {
        const val KEY_CATEGORY_ID = "category_id"

        fun newInstance(categoryId: Int): MenuFragment {
            val bundle = Bundle()
            bundle.putInt(KEY_CATEGORY_ID, categoryId)
            val fragment = MenuFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    var categoryid = 0
    private val productList = ArrayList<ProductEntity>()
    private var _binding: FragmentMenuBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: TransactionViewModel

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DataBindingUtil.inflate(inflater, R.layout.fragment_menu, container, false)
        arguments?.apply {
            categoryid = getInt(KEY_CATEGORY_ID)
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if ((activity as MainActivity).getTransactionMain() == null) {
            return
        }

        Timber.d("[menu] categoryId: $categoryid")
        viewModel = (activity as MainActivity).getTransactionMain()!!.viewModel

        binding.recviewMenu.adapter = object : GlobalAdapter<ListItemChooseMenuBinding>(
            R.layout.list_item_choose_menu,
            productList,
            binding.incLayNoData.root
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemChooseMenuBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                val product = productList[holder.adapterPosition]

                holder.binding.imgAdd.setOnClickListener {
                    checkStock(product) {
                        addBill(product)
                    }
                }

                holder.binding.imgMinus.setOnClickListener {
                    val variants = ArrayList<ProductEntity>()
                    viewModel.orders.asSequence()
                        .filter { p -> p.product?.productId == product.productId }
                        .distinctBy { p -> p.product?.productDetailId }.toList().forEach { order ->
                            order.product?.let { variants.add(it) }
                        }

                    if (variants.size > 1) {
                        val variantNames = variants.map { it.name }
                        AlertDialog.Builder(context!!)
                            .setTitle(getString(R.string.choose_variant))
                            .setNegativeButton(R.string.cancel, null)
                            .setItems(variantNames.toTypedArray()) { _, position ->
                                removeItemOrder(variants[position].productDetailId, holder)
                            }
                            .show()
                    } else {
                        variants.firstOrNull()
                            ?.let { variant -> removeItemOrder(variant.productDetailId, holder) }
                    }
                }

                val visibility =
                    viewModel.orders.filter { it.product?.productId == product.productId }
                        .takeIf { it.isNotEmpty() }?.let { order ->
                            holder.binding.edtQty.setText(order.filter { !it.isItemVoid }
                                .sumOf { abs(it.qty) }.toString())
                            View.VISIBLE
                        } ?: kotlin.run {
                        View.GONE
                    }

                holder.itemView.setOnClickListener {
                    (activity as MainActivity).getTransactionMain()?.showProductDetail(product)
                }

                holder.binding.imgMinus.visibility = visibility
                holder.binding.edtQty.visibility = visibility
            }
        }

        binding.recviewMenu.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                viewModel.refreshFabScan.postValue(dy < 0)
            }
        })

        if (savedInstanceState == null) {
            refreshMenuList()
        }

        Timber.i(">>> Load Menu With ID : $categoryid")
    }

    private fun removeItemOrder(
        productDetailId: Int,
        holder: GlobalViewHolder<ListItemChooseMenuBinding>
    ) {
        viewModel.orders.firstOrNull { it.product?.productDetailId == productDetailId }
            ?.let { order ->
                val realQty =
                    viewModel.orders.filter { it.product?.productDetailId == productDetailId }
                        .sumOf { if (it.isItemVoid) abs(it.qty) * -1 else abs(it.qty) }
                Timber.d("qty current: ${order.qty} - realQty: $realQty - hold: ${order.holdQty}")
                if ((order.qty - 1) >= order.holdQty) {
                    val qtyAllVariant = getQty(order.product) - 1
                    order.qty = order.qty - 1

                    if (order.qty <= 0) {
                        viewModel.orders.remove(order)
                        viewModel.updateTax(
                            order,
                            viewModel.calculateDiscPerItem(order, order),
                            false
                        )
                    }

                    if (qtyAllVariant <= 0) {
                        holder.binding.imgMinus.visibility = View.GONE
                        holder.binding.edtQty.visibility = View.GONE
                        viewModel.updateTax(
                            order,
                            viewModel.calculateDiscPerItem(order, order),
                            false
                        )
                    } else {
                        viewModel.updatePrice(order)
                        viewModel.updateTax(
                            order,
                            viewModel.calculateDiscPerItem(order, order),
                            false
                        )
                        holder.binding.edtQty.setText(qtyAllVariant.toString())
                    }
                } else {
                    context?.toast("silahkan lakukan void dari menu order summary")
                }
            }
        viewModel.refreshOrder.call()
    }

    fun addBill(
        product: ProductEntity,
        isNeedToCheckVariant: Boolean = true,
        isNeedToCheckLinkMenu: Boolean = true,
        isHasLinkMenu: Boolean = false,
        qty: Int = 1
    ) {
//        Timber.d("this will add to bill : ${Gson().toJson(viewModel.products.filter { it.productId == product.productId })}")
        if (isNeedToCheckVariant) {
            if (viewModel.isHasVariant(product.productId)) {
                object : ChooseVariantDialog(requireContext(), viewModel, product.productId) {
                    override fun onItemSelected(productEntity: ProductEntity) {
                        addBill(productEntity, isNeedToCheckVariant = false)
                    }
                }.show()
                return
            }
        }

        if (isNeedToCheckLinkMenu) {
            val linkMenu =
                viewModel.linkMenuList.filter { it.productDetailFkid == product.productDetailId }
            if (linkMenu.isNotEmpty()) {
                object : LinkMenuDialog(requireContext(), viewModel) {
                    override fun addToBill(
                        productEntity: ProductEntity,
                        linkMenuSelected: List<LinkMenuProduct>,
                        qty: Int
                    ) {
                        addBill(
                            productEntity,
                            isNeedToCheckVariant = false,
                            isNeedToCheckLinkMenu = false,
                            isHasLinkMenu = true,
                            qty = qty
                        )
                        linkMenuSelected.forEach {
                            addExtra(
                                it.product.copy(priceSell = it.linkMenuDetail.priceAdd),
                                "link"
                            )
                        }
                    }
                }.showLinkMenu(product)
                return
            }
        }

        viewModel.orders.takeIf { !isHasLinkMenu }
            ?.firstOrNull { it.product?.productDetailId == product.productDetailId }?.let {
                it.qty = it.qty + 1
                viewModel.updatePrice(it)
                viewModel.updateTax(it, viewModel.calculateDiscPerItem(it, it))
                viewModel.refreshOrder.call()
            } ?: kotlin.run {
            viewModel.orders.add(Order(product, qty))
            viewModel.updatePrice(viewModel.orders.last())
            viewModel.updateTax(viewModel.orders.last())
            viewModel.refreshOrder.call()
        }
        binding.recviewMenu.adapter?.notifyDataSetChanged()
    }

    //tag : can be extra or link
    private fun addExtra(productEntity: ProductEntity, tag: String = "extra") {
        Timber.i(">> Add Extra : ${productEntity.name}")
        val order = Order(
            productEntity, extra = ArrayList(), employeeId = context?.employee()?.employeeId
                ?: 0, qty = 1, extraType = tag
        )
//        val disc = viewModel.calculateDiscPerItem(order, viewModel.orders[viewModel.orders.size - 1])
        viewModel.orders.lastOrNull()?.let { orderParent ->
            viewModel.updatePrice(order)
            viewModel.updateTax(order, viewModel.calculateDiscPerItem(order, orderParent))
            viewModel.orders.lastOrNull()?.extra?.add(order)
        }
    }

    private fun getQty(product: ProductEntity?): Int {
        return viewModel.orders.filter { !it.isItemVoid && it.product?.productId == product?.productId }
            .sumOf { it.qty }
    }

    fun checkStock(product: ProductEntity, listener: () -> Unit) {
        if (!viewModel.isHasVariant(product.productId) && (product.stock == Constant.STOCK_UNAVAILABLE || (product.stockQty == 0 && product.stockManagement == 1))) {
            if(requireContext().outletFeature().transactionZeroStock){
                context?.showMessage(
                    getString(R.string.question_adding_no_stock_product),
                    getString(R.string.run_out),
                    { _, _ ->
                        listener()
                    })
            }else{
                context?.showMessage(getString(R.string.stock_out_warning), getString(R.string.run_out))
            }
        } else {
            listener()
        }
    }

    fun refreshMenuList() {
        productList.clear()
        if (categoryid < 0) productList.addAll(viewModel.productsUnique)
        else viewModel.productsUnique.filter { it.productSubcategoryFkid == categoryid }.let {
            productList.addAll(it)
        }
        binding.recviewMenu.adapter?.notifyDataSetChanged()
        binding.recviewMenu.post { viewModel.refreshFabScan.postValue(true) }
    }

    fun search(query: String?) {
        if(categoryid == Constant.MenuCategoryPromo){
            searchPromotion(query)
        }else{
            searchProduct(query)
        }
    }

    private fun searchProduct(query: String?){
        query?.takeIf { it.isNotEmpty() }?.let {
            productList.clear()
            val products = viewModel.productsUnique.filter { p -> searchProductByKeyword(query, p) }
            productList.addAll(if (categoryid >= 0) products.filter { it.productSubcategoryFkid == categoryid } else products)
            binding.recviewMenu.adapter?.notifyDataSetChanged()
        } ?: kotlin.run { refreshMenuList() }
    }

    private fun searchPromotion(query: String?){

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
